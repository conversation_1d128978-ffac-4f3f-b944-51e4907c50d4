import UIKit
import TXLiteAVSDK_UGC
import SnapKit // 添加导入
import AVFoundation // 新增导入

// 修改继承自 BaseViewController, 并添加 RecordSettingsPopupViewDelegate
class VideoRecordViewController: BaseViewController, TXUGCRecordListener, BeautyPanelViewDelegate, RecordSettingsPopupViewDelegate, FilterPanelViewDelegate, MusicPanelViewDelegate {
    func filterPanelDidTouchOriginal(_ state: UIGestureRecognizer.State) {
        
    }
    

    // MARK: - UI Components
    private lazy var previewView: UIView = {
        let view = UIView()
        view.backgroundColor = .black
        return view
    }()

    private lazy var customNavBar: UIView = {
        let view = UIView()
        view.backgroundColor = .clear // 透明背景
        return view
    }()
    
    // 1. 属性区
    private let modeTitles = ["笔记", "视频"]
    private var selectedModeIndex = 1 // 默认"视频"
    private lazy var modeCollectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 32
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(ModeCell.self, forCellWithReuseIdentifier: "ModeCell")
        return collectionView
    }()
    private lazy var modeDot: UIView = {
        let dot = UIView()
        dot.backgroundColor = .white
        dot.layer.cornerRadius = 2
        dot.clipsToBounds = true
        return dot
    }()

    private lazy var closeButton: UIButton = {
        let button = UIButton(type: .custom)
        // 使用系统关闭图标，你需要确认是否有 'close_icon' 图片资源，或者使用系统自带的
        if let closeIcon = UIImage(named: "close_icon") {
             button.setImage(closeIcon, for: .normal)
        } else {
             button.setTitle("✕", for: .normal) // 备用文本
             button.setTitleColor(.white, for: .normal)
             button.titleLabel?.font = .systemFont(ofSize: 24, weight: .bold)
        }
        button.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
        return button
    }()

    // 新增：底部蒙版容器
    private lazy var bottomMaskContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        view.clipsToBounds = true // 确保模糊效果不出界
        return view
    }()

    // 新增：模糊效果视图
    private lazy var blurEffectView: UIVisualEffectView = {
        let blurEffect = UIBlurEffect(style: .dark) // 使用深色模糊
        let view = UIVisualEffectView(effect: blurEffect)
        view.alpha = 0.8 // 调低透明度，让毛玻璃更通透
        return view
    }()

    // 新增：半透明黑色覆盖层
    private lazy var overlayView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.4) // 30% 透明度黑色，控制整体通透度
        return view
    }()

    // 新增：顶部水平分隔线
    private lazy var topSeparatorLine: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white.withAlphaComponent(0.1) // 10% 透明度白色
        return view
    }()

    // 新增：底部水平分隔线
    private lazy var bottomSeparatorLine: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white.withAlphaComponent(0.1) // 10% 透明度白色
        return view
    }()

    // 新增：录制按钮
    private lazy var recordButton: UIButton = {
        let button = UIButton(type: .custom)
        button.backgroundColor = .clear // 改为透明
        button.setImage(UIImage(named: "record_shoot_play"), for: .normal) // 设置初始图标
        button.setImage(UIImage(named: "record_shoot_stop"), for: .selected) // 设置录制状态图标
        button.addTarget(self, action: #selector(recordButtonTapped), for: .touchUpInside)
        return button
    }()

    // 新增：相册按钮
    private lazy var albumButton: UIButton = {
        let button = UIButton(type: .custom)
        button.backgroundColor = .clear // 改为透明
        button.layer.cornerRadius = 28 // 56 / 2
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.white.withAlphaComponent(0.8).cgColor
        button.setImage(UIImage(named: "record_photo"), for: .normal) // 设置图标
        // 设置内边距使图标居中显示为 36x36
        let inset: CGFloat = (56 - 36) / 2
        button.imageEdgeInsets = UIEdgeInsets(top: inset, left: inset, bottom: inset, right: inset)
        button.addTarget(self, action: #selector(albumButtonTapped), for: .touchUpInside)
        return button
    }()

    // 新增：翻转摄像头按钮
    private lazy var flipCameraButton: UIButton = {
        let button = UIButton(type: .custom)
        button.backgroundColor = .clear // 改为透明
        button.layer.cornerRadius = 28 // 56 / 2
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.white.withAlphaComponent(0.8).cgColor
        button.setImage(UIImage(named: "record_camera_sw"), for: .normal) // 设置图标
        // 设置内边距使图标居中显示为 36x36
        let inset: CGFloat = (56 - 36) / 2
        button.imageEdgeInsets = UIEdgeInsets(top: inset, left: inset, bottom: inset, right: inset)
        button.addTarget(self, action: #selector(flipCameraButtonTapped), for: .touchUpInside)
        return button
    }()

    // 新增：美颜按钮
    private lazy var beautyButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("美颜", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 13)
        button.addTarget(self, action: #selector(topActionButtonTapped(_:)), for: .touchUpInside)
        button.tag = 10 // 用于区分按钮
        return button
    }()

    // 新增：滤镜按钮
    private lazy var filterButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("滤镜", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 13)
        button.addTarget(self, action: #selector(topActionButtonTapped(_:)), for: .touchUpInside)
        button.tag = 11
        return button
    }()

    // 新增：音乐选择Bar
    private lazy var musicSelectBar: MusicSelectBar = {
        let bar = MusicSelectBar()
        bar.translatesAutoresizingMaskIntoConstraints = false
        bar.onTap = { [weak self] in
            self?.presentPanel(for: 12)
        }
        bar.onClear = { [weak self] in
            self?.setSelectedMusic(nil)
            self?.bgmPlayer?.stop()
            self?.bgmPlayer = nil
            self?.musicPanelView.clearSelectedMusic()
            // 恢复麦克风收音
            self?.toggleMic(false)
            self?.micButton.isSelected = false
            self?.isMicOn = true
        }
        return bar
    }()

    // 新增：设置按钮 (导航栏)
    private lazy var settingsButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "record_setting"), for: .normal)
        button.addTarget(self, action: #selector(settingsButtonTapped), for: .touchUpInside)
        return button
    }()

    // 新增：闪光灯按钮 (导航栏)
    private lazy var flashButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "record_toggle_close"), for: .normal) // 默认关闭
        button.setImage(UIImage(named: "record_toggle_open"), for: .selected) // 选中时打开
        button.addTarget(self, action: #selector(flashButtonTapped), for: .touchUpInside)
        return button
    }()

    // 新增：麦克风按钮 (导航栏)
    private lazy var micButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "record_mic_open"), for: .normal) // 默认打开
        button.setImage(UIImage(named: "record_mic_close"), for: .selected) // 选中时关闭(静音)
        button.addTarget(self, action: #selector(micButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var mirrorButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "record_mirror_open"), for: .selected) // 选中为开启
        button.setImage(UIImage(named: "record_mirror_close"), for: .normal) // 未选中为关闭
        button.addTarget(self, action: #selector(mirrorButtontapped), for: .touchUpInside)
        return button
    }()

    // 新增：录制计时器标签
    private lazy var recordingTimerLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .white
        label.text = "00:00"
        label.textAlignment = .center
        label.isHidden = true // 初始隐藏
        return label
    }()

    // 新增：透明覆盖层（用于点击空白区域关闭面板，但不显示遮罩颜色）
    private lazy var dimmingOverlayView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.clear // 改为完全透明
        view.alpha = 0 // 初始透明
        view.isHidden = true
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleDimmingOverlayTap))
        view.addGestureRecognizer(tapGesture)
        return view
    }()

    // 新增：美颜面板
    private lazy var beautyPanelView: BeautyPanelView = {
        let view = BeautyPanelView()
        view.isHidden = true
        return view
    }()

    // 新增：滤镜面板
    private lazy var filterPanelView: FilterPanelView = {
        let view = FilterPanelView()
        view.delegate = self
        view.isHidden = true
        return view
    }()

    // 新增：音乐面板 - 使用封装的MusicPanelView
    private lazy var musicPanelView: MusicPanelView = {
        let view = MusicPanelView()
        view.delegate = self
        view.isHidden = true
        return view
    }()

    // 新增：网格视图 （使用封装后的类）
    private lazy var gridView: RecordGridView = {
        let grid = RecordGridView()
        grid.isHidden = true // 默认隐藏
        return grid
    }()

    // 新增：设置弹窗 （使用封装后的类）
    private lazy var settingsPopupView: RecordSettingsPopupView = {
        let popup = RecordSettingsPopupView()
        popup.delegate = self
        return popup
    }()
    
    // 新增：比例选择弹窗
    private lazy var aspectRatioSelectorView: AspectRatioSelectorView = {
        let view = AspectRatioSelectorView()
        view.isHidden = true
        return view
    }()
    
    // 新增：定时选择弹窗
    private lazy var timerSelectorView: TimerSelectorView = {
        let view = TimerSelectorView()
        view.isHidden = true
        return view
    }()
    
    // MARK: - 弹窗类型枚举
    private enum PopupType {
        case none
        case settings
        case aspectRatio
        case timer
    }
    private var currentPopup: PopupType = .none

    // 统一遮罩View
    private lazy var popupMaskView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        view.isHidden = true
        let tap = UITapGestureRecognizer(target: self, action: #selector(handlePopupMaskTap))
        view.addGestureRecognizer(tap)
        return view
    }()

    // MARK: - Properties
    private var isFrontCamera = true // 默认为前置摄像头
    private var isTorchOn = false // 闪光灯状态
    private var isMicOn = true // 麦克风状态 (true = 未静音)
    private var isRecording = false // 录制状态
    private var recordingTimer: Timer? // 录制计时器
    private var recordingDuration: Int = 0 // 录制时长（秒）
    private var isPanelPresented = false // 面板是否已显示
    private var currentPresentedPanel: UIView? // 当前显示的面板

    // 新增：跟踪 gridView 约束是否已设置
    private var gridViewConstraintsSet = false

    // 新增：currentAspectRatio 属性
    private var currentAspectRatio: AspectRatioOption = .ratio9_16

    // 新增：镜像状态变量
    private var isMirrorEnabled = false

    // 新增：音量键拍摄功能开关
    private var isVolumeCaptureEnabled = false

    // 在合适位置（如属性区）添加：
    private let volumeKeyManager = VolumeKeyCaptureManager()

    // 新增：本地BGM试听播放器
    private var bgmPlayer: AVAudioPlayer?
    // 新增：当前选中的BGM
    private var selectedBGM: MusicItem?

    // 新增：定时倒计时相关
    private var currentTimerValue: Int = 0 // 当前设置的定时秒数
    private var countdownTimer: Timer? // 倒计时定时器
    private var countdownValue: Int = 0 // 当前倒计时值
    private var isCountingDown: Bool = false // 是否正在倒计时

    // 新增：倒计时标签
    private lazy var countdownLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 64, weight: .bold)
        label.textColor = .white
        label.textAlignment = .center
        label.isHidden = true
        label.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        label.layer.cornerRadius = 8
        label.clipsToBounds = true
        return label
    }()

    // MARK: - Lifecycle
    override func viewDidLoad() {
        // 在super.viewDidLoad前初始化currentAspectRatio
        currentAspectRatio = initialAspectRatioFromStorage()
        super.viewDidLoad()
        useFullCustomLayout = true
        setupUI()
        setupCamera()
        view.addSubview(popupMaskView)
        view.addSubview(settingsPopupView)
        view.addSubview(aspectRatioSelectorView)
        view.addSubview(timerSelectorView)
        popupMaskView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        settingsPopupView.snp.makeConstraints { make in
            make.width.equalTo(172)
            make.top.equalTo(customNavBar.snp.bottom).offset(5)
            make.trailing.equalTo(settingsButton)
        }
        aspectRatioSelectorView.snp.makeConstraints { make in
            make.width.equalTo(183)
            make.height.equalTo(85)
            make.top.equalTo(customNavBar.snp.bottom).offset(5)
            make.trailing.equalTo(settingsButton)
        }
        timerSelectorView.snp.makeConstraints { make in
            make.width.equalTo(183)
            make.height.equalTo(85)
            make.top.equalTo(customNavBar.snp.bottom).offset(5)
            make.trailing.equalTo(settingsButton)
        }
        settingsPopupView.isHidden = true
        aspectRatioSelectorView.isHidden = true
        timerSelectorView.isHidden = true
        currentAspectRatio = .ratio9_16
        aspectRatioSelectorView.setSelected(currentAspectRatio)
        aspectRatioSelectorView.onSelect = { [weak self] option in
            guard let self = self else { return }
            self.currentAspectRatio = option
            self.aspectRatioSelectorView.setSelected(option)
            self.settingsPopupView.setAspectRatioLabelText(option.title)
            // SDK切换比例
            let sdkRatio: TXVideoAspectRatio
            switch option {
            case .ratio1_1: sdkRatio = .VIDEO_ASPECT_RATIO_1_1
            case .ratio3_4: sdkRatio = .VIDEO_ASPECT_RATIO_3_4
            case .ratio9_16: sdkRatio = .VIDEO_ASPECT_RATIO_9_16
            case .ratio16_9: sdkRatio = .VIDEO_ASPECT_RATIO_16_9
            }
            TXUGCRecord.shareInstance().setAspectRatio(sdkRatio)
            // 切换比例时，动态更新网格高度
            self.updateGridViewHeight(for: option)
            // 关键：保存到本地
            UserDefaults.standard.set(option.title, forKey: RecordSettingsKeys.aspectRatio)
            print("[保存比例] 已保存比例：\(option.title)")
        }
        timerSelectorView.onSelect = { [weak self] option in
            guard let self = self else { return }
            // 选中后更新设置弹窗的定时文案
            self.settingsPopupView.setTimerLabelText(option.title)
            // 保存定时设置
            self.currentTimerValue = option.rawValue
            UserDefaults.standard.set(option.title, forKey: RecordSettingsKeys.timerValue)
            print("[定时设置] 保存定时值: \(option.title) (\(option.rawValue)秒)")
            self.hideTimerSelector()
        }
        do {
            try AVAudioSession.sharedInstance().setCategory(.playback, mode: .default)
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("AVAudioSession设置失败: \(error)")
        }
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        startCameraPreview() // 视图出现时启动预览
        syncSettingsPopupState() // 同步设置弹窗状态

        // 根据全局开关状态设置音量键监听
        let enabled = UserDefaults.standard.bool(forKey: RecordSettingsKeys.volumeCaptureEnabled)
        volumeKeyManager.isEnabled = enabled

        // 确保音量键监听器已初始化并根据需要启动
        if enabled {
            print("[音量键拍摄] 启动监听")
            volumeKeyManager.start()
        }

        // 设置音量键松手时的回调
        volumeKeyManager.onToggleRecord = { [weak self] in
            guard let self = self else { return }
            // 只允许在非面板弹出时切换
            guard !self.isPanelPresented else {
                print("[音量键拍摄] 面板显示中，无法切换录制状态")
                return
            }
            print("[音量键拍摄] 切换录制状态")
            // 切换录制状态
            self.recordButtonTapped()
        }
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        print("[音量键拍摄] viewWillAppear 被调用")

        // 恢复UI状态（从编辑页面返回时）
        if isCountingDown {
            // 如果还在倒计时中，停止倒计时并恢复UI
            print("[UI恢复] 检测到倒计时状态，停止倒计时并恢复UI")
            stopCountdown()
            updateBottomUI(isHidden: false, animated: false)
            customNavBar.alpha = 1
        } else {
            // 确保UI处于正常状态
            updateBottomUI(isHidden: false, animated: false)
            customNavBar.alpha = 1
        }

        // 确保UI状态完全正确
        ensureUIStateCorrect()

        let enabled = UserDefaults.standard.bool(forKey: RecordSettingsKeys.volumeCaptureEnabled)
        volumeKeyManager.isEnabled = enabled
        // 关键：每次都先 stop 再 start，彻底重置监听
        volumeKeyManager.stop()
        if enabled {
            print("[音量键拍摄] viewWillAppear 启动监听")
            volumeKeyManager.start()
        }
        volumeKeyManager.onToggleRecord = { [weak self] in
            guard let self = self else { return }
            guard !self.isPanelPresented else {
                print("[音量键拍摄] 面板显示中，无法切换录制状态")
                return
            }
            print("[音量键拍摄] 切换录制状态")
            self.recordButtonTapped()
        }
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        stopCameraPreview() // 视图消失前停止预览

        // 停止音量键监听
        print("[音量键拍摄] 停止监听")
        volumeKeyManager.stop()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        guard let layout = modeCollectionView.collectionViewLayout as? UICollectionViewFlowLayout else { return }
        let cellWidth = (modeTitles[selectedModeIndex] as NSString).size(withAttributes: [.font: UIFont.boldSystemFont(ofSize: 20)]).width + 32
        let inset = (modeCollectionView.bounds.width - cellWidth) / 2
        layout.sectionInset = UIEdgeInsets(top: 0, left: max(inset, 0), bottom: 0, right: max(inset, 0))
        layout.invalidateLayout()
        // 保证选中项居中
        modeCollectionView.scrollToItem(at: IndexPath(item: selectedModeIndex, section: 0), at: .centeredHorizontally, animated: false)
    }

    deinit {
        // 防止push返回时残留监听
        print("[音量键拍摄] deinit移除监听")
        volumeKeyManager.stop()
    }

    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .black // 背景设为黑色

        // 添加视图
        view.addSubview(previewView)
        view.addSubview(bottomMaskContainer)
//        bottomMaskContainer.isHidden = true
        bottomMaskContainer.addSubview(blurEffectView)
        bottomMaskContainer.addSubview(overlayView)
        bottomMaskContainer.addSubview(topSeparatorLine)
        bottomMaskContainer.addSubview(bottomSeparatorLine)
        
        view.addSubview(customNavBar)
        customNavBar.addSubview(closeButton)

        // 在 previewView 上添加网格视图并设置初始约束
        previewView.addSubview(gridView)
        // 立即设置网格视图的约束，基于当前比例
        updateGridViewHeight(for: currentAspectRatio)
        gridViewConstraintsSet = true

        // --- 在蒙版容器中添加按钮 ---
        bottomMaskContainer.addSubview(recordButton)
        bottomMaskContainer.addSubview(albumButton)
        bottomMaskContainer.addSubview(flipCameraButton)

//        bottomMaskContainer.addSubview(segmentStackView)
//        bottomMaskContainer.addSubview(segmentDot)
        
        // 2. setupUI() 方法体内
        bottomMaskContainer.addSubview(modeCollectionView)
        bottomMaskContainer.addSubview(modeDot)
        modeCollectionView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.equalTo(bottomSeparatorLine.snp.bottom).offset(10)
            make.height.equalTo(44)
        }
        modeDot.snp.remakeConstraints { make in
            make.top.equalTo(modeCollectionView.snp.bottom).offset(-modeCollectionView.bounds.height + 4 + 44) // 44为cell高度
            make.width.height.equalTo(4)
            make.centerX.equalToSuperview()
        }
        DispatchQueue.main.async {
            self.modeCollectionView.reloadData()
            self.modeCollectionView.layoutIfNeeded()
            self.modeCollectionView.scrollToItem(at: IndexPath(item: self.selectedModeIndex, section: 0), at: .centeredHorizontally, animated: false)
            self.moveDotToSelectedCell()
        }

        // --- 在蒙版容器中添加顶部按钮 ---
        bottomMaskContainer.addSubview(beautyButton)
        bottomMaskContainer.addSubview(filterButton)
        // 悬浮添加musicSelectBar到主view
        view.addSubview(musicSelectBar)
        musicSelectBar.snp.makeConstraints { make in
            make.bottom.equalTo(bottomMaskContainer.snp.top).offset(-10)
            make.centerX.equalToSuperview()
            make.width.greaterThanOrEqualTo(100)
            make.width.lessThanOrEqualTo(150)
            make.height.equalTo(34)
        }

        // --- 在蒙版容器中添加导航栏右侧按钮 ---
        customNavBar.addSubview(settingsButton)
        customNavBar.addSubview(flashButton)
        customNavBar.addSubview(micButton)
        customNavBar.addSubview(mirrorButton)

        // --- 添加计时器标签 ---
        bottomMaskContainer.addSubview(recordingTimerLabel)

        // --- 添加 Dimming Overlay 和 Panel Views 到主视图层级 ---
        view.addSubview(dimmingOverlayView)
        view.addSubview(beautyPanelView)
        view.addSubview(filterPanelView)
        view.addSubview(musicPanelView)

        // 添加倒计时标签
        view.addSubview(countdownLabel)
        
        // 添加设置弹窗和遮罩
        view.addSubview(popupMaskView)
        view.addSubview(settingsPopupView)
        view.addSubview(aspectRatioSelectorView)
        view.addSubview(timerSelectorView)

        // --- 使用 SnapKit 设置约束 ---
        previewView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        dimmingOverlayView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 面板初始约束（屏幕外底部）
        [beautyPanelView, filterPanelView, musicPanelView].forEach { panel in
            panel.snp.makeConstraints { make in
                make.leading.trailing.equalToSuperview()
                // 高度稍后在 present 时确定
                make.height.equalTo(150) // 初始占位高度，稍后修改
                make.top.equalTo(view.snp.bottom)
            }
        }

        bottomMaskContainer.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(246) // 初始高度
        }

        blurEffectView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        overlayView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        topSeparatorLine.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(40)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(1)
        }

        bottomSeparatorLine.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.bottom.equalToSuperview() // 初始底部偏移为 0
            make.height.equalTo(1)
        }

        // --- 按钮约束 --- (这些都在 bottomMaskContainer 内)
        recordButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(topSeparatorLine.snp.bottom).offset(42)
            make.size.equalTo(80)
        }

        albumButton.snp.makeConstraints { make in
            make.centerY.equalTo(recordButton)
            make.trailing.equalTo(recordButton.snp.leading).offset(-54)
            make.size.equalTo(56)
        }

        flipCameraButton.snp.makeConstraints { make in
            make.centerY.equalTo(recordButton)
            make.leading.equalTo(recordButton.snp.trailing).offset(54)
            make.size.equalTo(56)
        }

        // --- 顶部按钮约束 --- (这些都在 bottomMaskContainer 内)
        let topButtonSpacing: CGFloat = 15
        beautyButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(20)
            make.bottom.equalTo(topSeparatorLine.snp.top)
            make.size.equalTo(CGSize(width: 64, height: 40))
        }
        filterButton.snp.makeConstraints { make in
            make.leading.equalTo(beautyButton.snp.trailing).offset(topButtonSpacing)
            make.centerY.equalTo(beautyButton)
            make.size.equalTo(CGSize(width: 64, height: 40))
        }
        musicSelectBar.snp.makeConstraints { make in
            make.bottom.equalTo(bottomMaskContainer.snp.top).offset(-10)
            make.centerX.equalToSuperview()
            make.width.greaterThanOrEqualTo(100)
            make.width.lessThanOrEqualTo(150)
            make.height.equalTo(34)
        }

        // --- 导航栏约束 --- (这些都在 customNavBar 内)
        customNavBar.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(44)
        }
        closeButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(15)
            make.centerY.equalToSuperview()
            make.size.equalTo(30)
        }
        settingsButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().offset(-15)
            make.centerY.equalToSuperview()
            make.size.equalTo(30)
        }
        flashButton.snp.makeConstraints { make in
            make.trailing.equalTo(settingsButton.snp.leading).offset(-12)
            make.centerY.equalToSuperview()
            make.size.equalTo(30)
        }
        micButton.snp.makeConstraints { make in
            make.trailing.equalTo(flashButton.snp.leading).offset(-12)
            make.centerY.equalToSuperview()
            make.size.equalTo(30)
        }
        mirrorButton.snp.makeConstraints { make in
            make.trailing.equalTo(micButton.snp.leading).offset(-12)
            make.centerY.equalToSuperview()
            make.size.equalTo(30)
        }

        // --- 计时器标签约束 --- (在 bottomMaskContainer 内)
        recordingTimerLabel.snp.makeConstraints { make in
            make.centerX.equalTo(recordButton)
            make.top.equalTo(recordButton.snp.bottom).offset(8)
        }

        // --- 倒计时标签约束 ---
        countdownLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(120)
            make.height.equalTo(120)
        }

        // 确保分隔线在模糊效果和覆盖层之上
        bottomMaskContainer.bringSubviewToFront(topSeparatorLine)
        bottomMaskContainer.bringSubviewToFront(bottomSeparatorLine)
        // 确保导航栏在最前面
        view.bringSubviewToFront(customNavBar)

        // 设置遮罩约束
        popupMaskView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 设置弹窗约束
        settingsPopupView.snp.makeConstraints { make in
            make.width.equalTo(172)
            // 注意：高度由内部约束决定，不再固定
            make.top.equalTo(customNavBar.snp.bottom).offset(5)
            make.trailing.equalTo(settingsButton) // Aligns with the settings button
        }

        aspectRatioSelectorView.snp.makeConstraints { make in
            make.width.equalTo(183)
            make.height.equalTo(85)
            make.top.equalTo(customNavBar.snp.bottom).offset(5)
            make.trailing.equalTo(settingsButton)
        }

        timerSelectorView.snp.makeConstraints { make in
            make.width.equalTo(183)
            make.height.equalTo(85)
            make.top.equalTo(customNavBar.snp.bottom).offset(5)
            make.trailing.equalTo(settingsButton)
        }

        // 确保分隔线和导航栏在最前面
        bottomMaskContainer.bringSubviewToFront(topSeparatorLine)
        bottomMaskContainer.bringSubviewToFront(bottomSeparatorLine)
        view.bringSubviewToFront(customNavBar)
        // 确保设置弹窗和遮罩在最前面（仅次于导航栏之下的其他面板）
        view.bringSubviewToFront(popupMaskView)
        view.bringSubviewToFront(settingsPopupView)
        view.bringSubviewToFront(aspectRatioSelectorView)
        view.bringSubviewToFront(timerSelectorView)

        // 设置 Beauty Panel 的 delegate
        beautyPanelView.delegate = self
        // 比例选择器初始选中本地配置
        aspectRatioSelectorView.setSelected(currentAspectRatio)

        bottomMaskContainer.addSubview(modeCollectionView)
        bottomMaskContainer.addSubview(modeDot)
        modeCollectionView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.top.equalTo(bottomSeparatorLine.snp.bottom).offset(10)
            make.height.equalTo(44)
        }
        modeDot.snp.remakeConstraints { make in
            make.top.equalTo(modeCollectionView.snp.bottom).offset(-modeCollectionView.bounds.height + 4 + 44) // 44为cell高度
            make.width.height.equalTo(4)
            make.centerX.equalToSuperview()
        }
        DispatchQueue.main.async {
            self.modeCollectionView.reloadData()
            self.modeCollectionView.layoutIfNeeded()
            self.modeCollectionView.scrollToItem(at: IndexPath(item: self.selectedModeIndex, section: 0), at: .centeredHorizontally, animated: false)
            self.moveDotToSelectedCell()
        }
    }

    private func setupCamera() {
        // 设置录制回调代理
        TXUGCRecord.shareInstance().recordDelegate = self
    }

    // MARK: - Camera Preview
    private func startCameraPreview() {
        // 配置相机参数（使用默认或常用设置）
        let config = TXUGCSimpleConfig()
        config.videoQuality = .VIDEO_QUALITY_HIGH // 高画质
        config.frontCamera = self.isFrontCamera  // 使用前置摄像头
        config.minDuration = 0 // 最小录制时长
        config.maxDuration = 30 // 最大录制时长 (10分钟)
        config.touchFocus = false // 禁用触摸对焦
        TXUGCRecord.shareInstance().setAspectRatio(.VIDEO_ASPECT_RATIO_9_16)
        // TXUGCRecord.shareInstance().setVideoRenderMode(.VIDEO_RENDER_MODE_ADJUST_RESOLUTION)

        // 设置镜像状态
        if isMirrorEnabled {
            TXUGCRecord.shareInstance().setVideoRenderMirrorType(.VIDEO_RENDER_MIRROR_TYPE_ENABLE)
            TXUGCRecord.shareInstance().setVideoEncoderMirror(true)
        } else {
            TXUGCRecord.shareInstance().setVideoRenderMirrorType(.VIDEO_RENDER_MIRROR_TYPE_AUTO)
            TXUGCRecord.shareInstance().setVideoEncoderMirror(false)
        }

        // 启动预览
        let result = TXUGCRecord.shareInstance().startCameraSimple(config, preview: previewView)
        if result == 0 {
             print("相机预览启动成功")
        } else {
             print("相机预览启动失败，错误码: \(result)")
             // 可以根据错误码进行处理，例如提示用户权限问题
             showPreviewErrorAlert(errorCode: result)
        }
        // 确保自定义导航栏在最前面
        view.bringSubviewToFront(customNavBar)
    }

    private func stopCameraPreview() {
        TXUGCRecord.shareInstance().stopCameraPreview()
        print("相机预览已停止")
    }

    // MARK: - Actions
    @objc private func closeButtonTapped() {
        // 如果面板显示中，先关闭面板
        if isPanelPresented {
            dismissPanel()
            return
        }
        stopCameraPreview() // 停止预览
        // 判断是 push 还是 present
        if let nav = self.navigationController, nav.viewControllers.first != self {
            nav.popViewController(animated: true)
        } else {
            dismiss(animated: true, completion: nil)
        }
    }

    @objc private func recordButtonTapped() {
        print("[录制按钮] 点击录制按钮，当前定时值: \(currentTimerValue)秒")

        // 如果面板显示中，不允许录制
        guard !isPanelPresented else {
            print("[录制按钮] 面板显示中，不允许录制")
            return
        }

        // 如果正在倒计时，不允许重复点击
        guard !isCountingDown else {
            print("[录制按钮] 正在倒计时中，不允许重复点击")
            return
        }

        if selectedModeIndex == 0 { // 笔记模式
            print("[录制按钮] 笔记模式，定时值: \(currentTimerValue)")
            // 检查是否需要定时拍照
            if currentTimerValue > 0 {
                print("[录制按钮] 开始定时拍照倒计时")
                startCountdown(for: .photo)
            } else {
                print("[录制按钮] 直接拍照")
                // 直接拍照
                takePhoto()
            }
            return
        }
        // 视频模式录制逻辑
        print("[录制按钮] 视频模式，当前isRecording=\(isRecording)，定时值: \(currentTimerValue)")

        if isRecording {
            // 停止录制
            print("[录制按钮] 停止录制")
            stopVideoRecording()
        } else {
            // 开始录制
            if currentTimerValue > 0 {
                print("[录制按钮] 开始定时录制倒计时")
                startCountdown(for: .video)
            } else {
                print("[录制按钮] 直接开始录制")
                startVideoRecording()
            }
        }
    }

    @objc private func albumButtonTapped() {
        // 如果面板显示中，先关闭面板
        if isPanelPresented {
            dismissPanel()
            return
        }
        print("相册按钮被点击")
        // TODO: 实现打开相册逻辑
        //打印当前是笔记还是视频
        print("当前是笔记模式: \(selectedModeIndex == 0)")
        if selectedModeIndex == 0 {
            // 笔记模式，打开相册
            print("笔记模式，打开相册")
            // 打开相册-获取照片
            let picker = UIImagePickerController()
            picker.sourceType = .photoLibrary
            picker.mediaTypes = ["public.image"]
            picker.delegate = self
            present(picker, animated: true, completion: nil)
        } else {
            // 视频模式，打开相册
            print("视频模式，打开相册")
            //打开相册只获取视频
            let picker = UIImagePickerController()
            picker.sourceType = .photoLibrary
            picker.mediaTypes = ["public.movie"]
            picker.delegate = self
            present(picker, animated: true, completion: nil)
        }
    }

    @objc private func flipCameraButtonTapped() {
         // 如果面板显示中，先关闭面板
        if isPanelPresented {
            dismissPanel()
            return
        }
        print("翻转摄像头按钮被点击")
        // TODO: 实现翻转摄像头逻辑
        switchCamera(!isFrontCamera) // 调用切换摄像头方法
    }

    @objc private func topActionButtonTapped(_ sender: UIButton) {
        // 如果当前已有面板显示，且点击的是同一个按钮，则关闭面板
        if isPanelPresented && currentPresentedPanel == getPanelView(for: sender.tag) {
            dismissPanel()
            return
        }
        // 如果当前已有面板显示，且点击的是不同的按钮，则先关闭当前面板，再打开新的
        else if isPanelPresented {
            dismissPanel() {
                self.presentPanel(for: sender.tag)
            }
            return
        }

        // 否则，直接显示面板
        presentPanel(for: sender.tag)
        // If opening music panel for the first time or after a while, load initial data
        if let panelView = getPanelView(for: sender.tag), panelView == musicPanelView, self.musicPanelView.musicItems.isEmpty {
            let isFavorites = false // 默认加载全部音乐
            self.musicPanelView.loadMusicData(isFavorites: isFavorites, refreshing: true)
        }
    }

    @objc private func flashButtonTapped() {
         // 如果面板显示中，先关闭面板
        if isPanelPresented {
            dismissPanel()
            return
        }
        print("闪光灯按钮被点击")
        toggleTorch(!isTorchOn) // 切换闪光灯状态
    }

    @objc private func micButtonTapped() {
         // 如果面板显示中，先关闭面板
        if isPanelPresented {
            dismissPanel()
            return
        }
        print("麦克风按钮被点击")
        // 调用 toggleMic，参数为按钮当前选中状态的反值
        toggleMic(!micButton.isSelected)
    }
    
    @objc private func mirrorButtontapped() {
        // 镜像点击，切换镜像状态
        print("镜像按钮被点击")
        isMirrorEnabled.toggle()
        UserDefaults.standard.set(isMirrorEnabled, forKey: RecordSettingsKeys.mirrorEnabled)

        // 设置预览镜像和录制镜像
        if isMirrorEnabled {
            // 开启镜像：预览镜像开启，录制也镜像
            TXUGCRecord.shareInstance().setVideoRenderMirrorType(.VIDEO_RENDER_MIRROR_TYPE_ENABLE)
            TXUGCRecord.shareInstance().setVideoEncoderMirror(true)
            print("镜像已开启：预览和录制都镜像")
        } else {
            // 关闭镜像：使用自动模式（前置镜像，后置不镜像），录制不镜像
            TXUGCRecord.shareInstance().setVideoRenderMirrorType(.VIDEO_RENDER_MIRROR_TYPE_AUTO)
            TXUGCRecord.shareInstance().setVideoEncoderMirror(false)
            print("镜像已关闭：使用自动模式，录制不镜像")
        }

        // 按钮选中状态：选中为开启镜像（显示open图标），未选中为关闭（显示close图标）
        mirrorButton.isSelected = isMirrorEnabled
    }

    @objc private func settingsButtonTapped() {
        if isPanelPresented {
            dismissPanel()
            return
        }
        if currentPopup == .aspectRatio {
            hideAspectRatioSelector(animated: false)
        }
        showSettingsPopup()
    }

    // MARK: - Panel Presentation/Dismissal

    private func getPanelView(for tag: Int) -> UIView? {
        switch tag {
        case 10: // 美颜
            return beautyPanelView
        case 11: // 滤镜
            return filterPanelView
        case 12: // 音乐
            return musicPanelView
        default:
            return nil
        }
    }

    // 根据 tag 获取对应的面板视图和基础高度
    private func getPanelInfo(for tag: Int) -> (view: UIView?, baseHeight: CGFloat) {
        // 注意：返回的是基础高度，在presentPanel方法中会加上底部安全区域高度
        switch tag {
        case 10: // 美颜
            // 美颜面板的基础高度
            return (beautyPanelView, 170)
        case 11: // 滤镜
            // 滤镜面板的基础高度，根据内容调整
            return (filterPanelView, 270)
        case 12: // 音乐
            // 音乐面板的基础高度
            return (musicPanelView, 450)
        default:
            return (nil, 0)
        }
    }

    // 根据 UIView 实例获取基础高度
    private func getBaseHeight(for panelView: UIView) -> CGFloat {
        if panelView == beautyPanelView {
            // 返回与 getPanelInfo 中一致的高度
            return 140 // 调整为估算的高度
        } else if panelView == filterPanelView {
            return 220
        } else if panelView == musicPanelView {
            return 450 // Increased height for music panel's new content
        } else {
            return 0 // 或者默认值
        }
    }

    private func presentPanel(for tag: Int, completion: (() -> Void)? = nil) {
        let panelInfo = getPanelInfo(for: tag)
        guard let panelView = panelInfo.view, panelInfo.baseHeight > 0, !isPanelPresented else {
             print("无法显示面板 - Tag: \(tag)")
             completion?()
             return
        }

        print("显示面板 - Tag: \(tag)")
        currentPresentedPanel = panelView
        isPanelPresented = true

        // 计算面板最终高度
        let bottomInset = view.safeAreaInsets.bottom
        let panelHeight = panelInfo.baseHeight + bottomInset

        // 隐藏底部UI
        updateBottomUI(isHidden: true)

        // 准备 Dimming View 和 Panel View
        dimmingOverlayView.isHidden = false
        panelView.isHidden = false
        view.bringSubviewToFront(dimmingOverlayView)
        view.bringSubviewToFront(panelView)

        // 修正：首次弹出音乐面板时自动加载音乐列表
        if panelView == musicPanelView, musicPanelView.musicItems.isEmpty {
            musicPanelView.loadMusicData(isFavorites: false, refreshing: true)
        }

        // 录制页专用：隐藏底部按钮
        if panelView == musicPanelView {
            musicPanelView.configureForRecordMode(true)
        }

        // 更新面板约束以显示在屏幕底部
        panelView.snp.remakeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(panelHeight) // 使用计算出的高度
        }

        // 动画显示
        UIView.animate(withDuration: 0.3, animations: {
            self.dimmingOverlayView.alpha = 1.0
            self.view.layoutIfNeeded() // 驱动约束动画
        }) { _ in
            // 新增：如果是音乐面板且有选中BGM，自动重新播放
            if panelView == self.musicPanelView, let selected = self.selectedBGM, let localURL = selected.localFileURL {
                do {
                    let player = try AVAudioPlayer(contentsOf: localURL)
                    player.numberOfLoops = -1
                    player.prepareToPlay()
                    player.play()
                    self.bgmPlayer = player
                    print("🎵 自动重新播放选中BGM: \(selected.title)")
                } catch {
                    print("🔴 自动播放BGM失败: \(error)")
                    self.bgmPlayer = nil
                }
            }
            completion?()
        }
    }

    @objc private func dismissPanel(animated: Bool = true, completion: (() -> Void)? = nil) {
        guard isPanelPresented, let panelView = currentPresentedPanel else {
            completion?()
            return
        }
        print("关闭面板")

        // 新增：如果是音乐面板，停止试听
        if panelView == musicPanelView {
            // 只有在未录制时才停止试听，录制中继续播放
            if !isRecording {
                bgmPlayer?.stop()
                bgmPlayer = nil
            }
        }

        // 获取当前面板的基础高度和安全区域高度
        let baseHeight = getBaseHeight(for: panelView)
        let bottomInset = view.safeAreaInsets.bottom
        let panelHeight = baseHeight + bottomInset

        let duration = animated ? 0.3 : 0

        // 更新面板约束回到屏幕外
        panelView.snp.remakeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(panelHeight) // 确保使用正确的高度
            make.top.equalTo(view.snp.bottom)
        }

        // 动画隐藏
        UIView.animate(withDuration: duration, animations: {
            self.dimmingOverlayView.alpha = 0
            self.view.layoutIfNeeded() // 驱动约束动画
        }) { [weak self] _ in
            guard let self = self else { return }
            self.dimmingOverlayView.isHidden = true
            panelView.isHidden = true
            self.isPanelPresented = false
            self.currentPresentedPanel = nil

            // 恢复底部UI（需要考虑录制状态）
            self.updateBottomUI(isHidden: false)
            completion?()
        }
    }

    // 辅助方法：统一控制底部UI显隐（动画）
    private func updateBottomUI(isHidden: Bool, animated: Bool = true) {
        let viewsToToggle: [UIView] = [
            albumButton, flipCameraButton, recordButton, // 包含录制按钮
            beautyButton, filterButton, musicSelectBar,
            topSeparatorLine, bottomSeparatorLine,
            blurEffectView, overlayView,
            recordingTimerLabel // 计时器也一起控制
        ]
        let targetAlpha: CGFloat = isHidden ? 0 : 1
        let duration = animated ? 0.3 : 0

        UIView.animate(withDuration: duration) {
            viewsToToggle.forEach { $0.alpha = targetAlpha }
            // 隐藏时禁用交互，显示时恢复
            viewsToToggle.forEach { $0.isUserInteractionEnabled = !isHidden }
        }

        // 确保录制按钮在恢复时总是可交互的（除非正在录制）
        if !isHidden {
            recordButton.isUserInteractionEnabled = true
            print("[UI恢复] 录制按钮交互已恢复")
        }

        // 根据录制状态，额外处理 recordButton 的 isHidden 和 isEnabled
        // 如果不是隐藏底部UI（即恢复时），并且当前正在录制，则确保录制按钮可见且禁用其他按钮
        if !isHidden && isRecording {
            // 再次调用 updateUIForRecordingState 来确保状态正确
            // 但这次调用前，确保视图是可见的 (alpha=1)
             DispatchQueue.main.async { // 确保在alpha动画之后执行
                self.updateUIForRecordingState() // 重新应用录制状态的显隐
            }
        } else if !isHidden && !isRecording {
            // 如果恢复时没有在录制，确保所有按钮都可用
            closeButton.isEnabled = true
            settingsButton.isEnabled = true
            flashButton.isEnabled = !isFrontCamera
            micButton.isEnabled = true
        }

    }


    // MARK: - Helper Methods
    // 修改 updateUIForRecordingState，使其只处理录制状态下的显隐，而不是完全隐藏
    private func updateUIForRecordingState() {
        recordButton.isSelected = isRecording

        // 定义在录制时需要隐藏/显示的视图数组
        let viewsToToggleDuringRecording: [UIView] = [
            albumButton, flipCameraButton,
            beautyButton, filterButton, musicSelectBar,
            topSeparatorLine, bottomSeparatorLine,
            blurEffectView, overlayView,
            recordingTimerLabel // 计时器也根据录制状态显示/隐藏
        ]

        // 录制时：隐藏其他按钮，显示计时器
        // 非录制时：显示其他按钮，隐藏计时器
        viewsToToggleDuringRecording.forEach { $0.isHidden = isRecording }
        recordingTimerLabel.isHidden = !isRecording // 计时器逻辑反转

        // 录制按钮本身不隐藏，只改变选中状态
        recordButton.isHidden = false
        // 关键：确保录制按钮始终可交互
        recordButton.isUserInteractionEnabled = true

        // 更新顶部导航栏按钮交互状态
        let topNavButtonsEnabled = !isRecording
        settingsButton.isEnabled = topNavButtonsEnabled
        flashButton.isEnabled = topNavButtonsEnabled && !isFrontCamera
        micButton.isEnabled = topNavButtonsEnabled
        mirrorButton.isEnabled = topNavButtonsEnabled
        closeButton.isEnabled = topNavButtonsEnabled

        // 确保修改了 isHidden 之后，透明度恢复
        viewsToToggleDuringRecording.forEach { $0.alpha = $0.isHidden ? 0 : 1 }
        recordButton.alpha = 1 // 录制按钮始终可见

        print("[录制状态] 录制按钮状态更新: isRecording=\(isRecording), isUserInteractionEnabled=\(recordButton.isUserInteractionEnabled)")

    }

    // MARK: - Error Handling
    private func showPreviewErrorAlert(errorCode: Int32) {
        var message = "无法启动相机预览。"
        switch errorCode {
        case -1:
            message += "\n原因：配置错误。"
        case -3:
            message += "\n原因：摄像头权限未授予，请在系统设置中开启。"
        case -4:
            message += "\n原因：麦克风权限未授予，请在系统设置中开启。"
        case -5:
            message += "\n原因：许可证信息无效，请检查您的许可证设置。"
        default:
            message += "\n错误码：\(errorCode)"
        }

        let alert = UIAlertController(title: "预览失败", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default) { [weak self] _ in
            // 确定后可以选择关闭页面
             self?.dismiss(animated: true)
        })
        // 确保在主线程弹出 Alert
        DispatchQueue.main.async {
             self.present(alert, animated: true)
        }
    }

    // MARK: - Camera Control (添加切换方法)
    private func switchCamera(_ isFront: Bool) {
        let result = TXUGCRecord.shareInstance().switchCamera(isFront)
        if result == true {
            self.isFrontCamera = isFront
            print("切换摄像头成功: \(isFront ? "前置" : "后置")")
            // 切换摄像头后，需要重置闪光灯状态（因为前置不支持闪光灯）
            if isFront && isTorchOn {
                toggleTorch(false)
            }
            // 更新闪光灯按钮的可用性（如果需要）
            flashButton.isEnabled = !isFront // 前置摄像头禁用闪光灯按钮
        } else {
            print("切换摄像头失败")
            // showAlert(title: "切换失败", message: "无法切换摄像头")
        }
    }

    // 新增：切换闪光灯方法
    private func toggleTorch(_ isOn: Bool) {
        // 只有后置摄像头支持闪光灯
        guard !isFrontCamera else {
            print("前置摄像头不支持闪光灯")
            // 确保按钮状态正确
            if isTorchOn {
                isTorchOn = false
                flashButton.isSelected = false
            }
            return
        }

        let result = TXUGCRecord.shareInstance().toggleTorch(isOn)
        if result {
            isTorchOn = isOn
            flashButton.isSelected = isOn
            print("闪光灯已 \(isOn ? "打开" : "关闭")")
        } else {
            print("切换闪光灯失败")
            // 可以添加错误提示
        }
    }

    // 新增：切换麦克风静音方法
    private func toggleMic(_ isMuted: Bool) { // 参数表示是否要静音
        TXUGCRecord.shareInstance().setMute(isMuted)
        isMicOn = !isMuted // isMicOn 表示麦克风 *开启*
        micButton.isSelected = isMuted // 选中状态表示 *静音*
        print("麦克风已 \(isMuted ? "静音" : "取消静音")")
    }

    // MARK: - TXUGCRecordListener
    func onRecordComplete(_ result: TXUGCRecordResult) {
        // 录制完成回调
        print("录制完成: \(result.retCode), \(result.descMsg)")
        isRecording = false // 更新录制状态
        DispatchQueue.main.async {
            self.updateUIForRecordingState() // 确保UI恢复
            self.stopRecordingTimer() // 停止计时器

            if result.retCode == .UGC_RECORD_RESULT_OK {
                print("录制成功，视频路径: \(result.videoPath)")
                // 确保 videoPath 有效 - result.videoPath 是 String，不是 Optional
                guard !result.videoPath.isEmpty else {
                    print("错误：录制成功但视频路径无效")
//                    self.showAlert(title: "录制错误", message: "无法获取有效的视频文件路径。")
                    return
                }
                // 跳转到编辑页面
                self.goToEditPage(videoPath: result.videoPath)
            } else {
                // 录制失败
                print("录制失败: \(result.descMsg)")
                // 可以显示一个错误提示给用户
//                self.showAlert(title: "录制失败", message: result.descMsg ?? "发生未知错误，录制失败。")
            }
        }
    }

    private func onRecordProgress(_ millisecond: Int64) {
        // 录制进度回调
        // print("录制进度: \(millisecond) ms")
    }

    private func onRecordEvent(_ event: Int) {
        // 录制事件回调
        print("录制事件: \(event)")
    }

    // 监听 safeAreaInsets 变化，更新蒙版高度和底部分隔线位置
    override func viewSafeAreaInsetsDidChange() {
        super.viewSafeAreaInsetsDidChange()
        let bottomInset = view.safeAreaInsets.bottom
        print("viewSafeAreaInsetsDidChange - Bottom inset: \(bottomInset)") // 调试日志

        // 更新蒙版容器的高度约束
        bottomMaskContainer.snp.updateConstraints { make in
            make.height.equalTo(246 + bottomInset)
        }

        // 更新底部水平分隔线的底部约束
        bottomSeparatorLine.snp.updateConstraints { make in
            make.bottom.equalToSuperview().offset(-bottomInset - 40)
        }

        // 可选：如果更新不立即生效，可以强制布局
        // view.layoutIfNeeded()
    }

    // MARK: - 新增：启动录制计时器
    private func startRecordingTimer() {
        recordingDuration = 0
        updateTimerLabelText()
        recordingTimerLabel.isHidden = false
        recordingTimer = Timer.scheduledTimer(timeInterval: 1.0, target: self, selector: #selector(updateTimerLabel), userInfo: nil, repeats: true)
    }

    // MARK: - 新增：停止录制计时器
    private func stopRecordingTimer() {
        recordingTimer?.invalidate()
        recordingTimer = nil
        recordingTimerLabel.isHidden = true
    }

    // MARK: - 新增：更新计时器标签的方法
    @objc private func updateTimerLabel() {
        recordingDuration += 1
        updateTimerLabelText()
    }

    // MARK: - 新增：格式化并更新计时器文本
    private func updateTimerLabelText() {
        let minutes = recordingDuration / 60
        let seconds = recordingDuration % 60
        recordingTimerLabel.text = String(format: "%02d:%02d", minutes, seconds)
    }

    // MARK: - 定时倒计时功能
    enum CountdownAction {
        case photo
        case video
    }

    private func startCountdown(for action: CountdownAction) {
        print("[定时倒计时] startCountdown被调用，当前定时值: \(currentTimerValue)")

        guard currentTimerValue > 0 else {
            print("[定时倒计时] 定时值为0，直接执行动作")
            // 如果定时值为0，直接执行动作
            executeAction(action)
            return
        }

        print("[定时倒计时] 开始倒计时: \(currentTimerValue)秒")
        isCountingDown = true
        countdownValue = currentTimerValue

        // 隐藏其他UI
        print("[定时倒计时] 隐藏UI")
        updateBottomUI(isHidden: true, animated: true)
        // 同时隐藏顶部导航栏
        UIView.animate(withDuration: 0.3) {
            self.customNavBar.alpha = 0
        }

        // 显示倒计时标签
        print("[定时倒计时] 显示倒计时标签: \(countdownValue)")
        countdownLabel.text = "\(countdownValue)"
        countdownLabel.isHidden = false
        countdownLabel.alpha = 0

        // 确保倒计时标签在最前面
        view.bringSubviewToFront(countdownLabel)

        // 动画显示倒计时标签
        UIView.animate(withDuration: 0.3) {
            self.countdownLabel.alpha = 1.0
        }

        // 启动倒计时定时器
        print("[定时倒计时] 启动定时器")
        countdownTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] timer in
            self?.updateCountdown(for: action)
        }
    }

    private func updateCountdown(for action: CountdownAction) {
        countdownValue -= 1
        print("[定时倒计时] 倒计时更新: \(countdownValue)")

        if countdownValue > 0 {
            // 更新倒计时显示
            countdownLabel.text = "\(countdownValue)"
            print("[定时倒计时] 显示数字: \(countdownValue)")

            // 添加缩放动画效果
            UIView.animate(withDuration: 0.2, animations: {
                self.countdownLabel.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
            }) { _ in
                UIView.animate(withDuration: 0.2) {
                    self.countdownLabel.transform = .identity
                }
            }
        } else {
            // 倒计时结束，执行动作
            print("[定时倒计时] 倒计时结束，执行动作: \(action)")
            stopCountdown()
            executeAction(action)
        }
    }

    private func stopCountdown() {
        print("[定时倒计时] 倒计时结束")
        countdownTimer?.invalidate()
        countdownTimer = nil
        isCountingDown = false

        // 隐藏倒计时标签
        UIView.animate(withDuration: 0.3, animations: {
            self.countdownLabel.alpha = 0
        }) { _ in
            self.countdownLabel.isHidden = true
        }
    }

    // 新增：确保UI状态正确恢复的方法
    private func ensureUIStateCorrect() {
        print("[UI状态] 确保UI状态正确")
        // 确保录制按钮可交互
        recordButton.isUserInteractionEnabled = true
        recordButton.alpha = 1.0

        // 确保其他按钮状态正确
        if !isRecording {
            albumButton.isUserInteractionEnabled = true
            flipCameraButton.isUserInteractionEnabled = true
            beautyButton.isUserInteractionEnabled = true
            filterButton.isUserInteractionEnabled = true

            // 确保顶部按钮状态正确
            settingsButton.isEnabled = true
            flashButton.isEnabled = !isFrontCamera
            micButton.isEnabled = true
            mirrorButton.isEnabled = true
            closeButton.isEnabled = true
        }
    }

    // 新增：重置定时设置为关闭状态（一次性使用）
    private func resetTimerToOff() {
        print("[定时设置] 重置定时为关闭状态")
        currentTimerValue = 0
        let offOption = TimerOption.off

        // 更新UI显示
        timerSelectorView.setSelected(offOption)
        settingsPopupView.setTimerLabelText(offOption.title)

        // 保存到本地
        UserDefaults.standard.set(offOption.title, forKey: RecordSettingsKeys.timerValue)
        print("[定时设置] 已重置定时设置为: \(offOption.title)")
    }

    private func executeAction(_ action: CountdownAction) {
        print("[执行动作] 开始执行动作: \(action)")

        // 先恢复UI状态
        updateBottomUI(isHidden: false, animated: true)
        UIView.animate(withDuration: 0.3) {
            self.customNavBar.alpha = 1
        }

        // 重置定时设置为关闭状态（一次性使用）
        resetTimerToOff()

        switch action {
        case .photo:
            takePhoto()
        case .video:
            startVideoRecording()
        }
    }

    private func takePhoto() {
        TXUGCRecord.shareInstance().snapshot { [weak self] image in
            print("[笔记模式] 拍照完成... image=\(image)")
            // 跳转到笔记发布页（不需要恢复UI，因为会离开当前页面）
            let notePublishVC = NoteEditingDetailsViewController(images: [image])
            self?.navigationController?.pushViewController(notePublishVC, animated: true)
        }
    }

    private func startVideoRecording() {
        isRecording = true
        updateUIForRecordingState()

        let result = TXUGCRecord.shareInstance().start()
        if result != 0 {
            print("启动录制失败，错误码: \(result)")
            isRecording = false
            updateUIForRecordingState()
            // 恢复UI
            updateBottomUI(isHidden: false, animated: true)
            // 同时恢复顶部导航栏
            UIView.animate(withDuration: 0.3) {
                self.customNavBar.alpha = 1
            }
        } else {
            print("开始录制...")
            startRecordingTimer()
            // 播放BGM
            if let selected = selectedBGM, let localURL = selected.localFileURL {
                do {
                    let player = try AVAudioPlayer(contentsOf: localURL)
                    player.numberOfLoops = -1
                    player.prepareToPlay()
                    player.play()
                    bgmPlayer = player
                    print("🎵 录制时BGM播放: \(selected.title)")
                } catch {
                    print("🔴 录制时BGM播放器初始化失败: \(error)")
                    bgmPlayer = nil
                }
            }
        }
    }

    private func stopVideoRecording() {
        TXUGCRecord.shareInstance().stop()
        print("停止录制...")
        isRecording = false
        updateUIForRecordingState()
        stopRecordingTimer()
        // 停止BGM播放
        bgmPlayer?.stop()
        bgmPlayer = nil
    }



    // MARK: - 新增：跳转到编辑页面的方法
    private func goToEditPage(videoPath: String) {
        print("[调试] goToEditPage 入参 videoPath: \(videoPath)")
        var finalPath = videoPath
        if FileManager.default.fileExists(atPath: videoPath) {
            // 检查是否为主App可访问的 tmp/documents 路径
            let isInAppSandbox = videoPath.hasPrefix(NSTemporaryDirectory()) || videoPath.hasPrefix(NSHomeDirectory())
            if !isInAppSandbox {
                // 拷贝到主App tmp 目录
                let fileName = (videoPath as NSString).lastPathComponent
                let destPath = NSTemporaryDirectory() + fileName
                do {
                    if FileManager.default.fileExists(atPath: destPath) {
                        try FileManager.default.removeItem(atPath: destPath)
                    }
                    try FileManager.default.copyItem(atPath: videoPath, toPath: destPath)
                    print("[调试] 已拷贝视频到主App tmp: \(destPath)")
                    finalPath = destPath
                } catch {
                    print("[调试] 拷贝视频失败: \(error)")
                    let alert = UIAlertController(title: "视频处理失败", message: "无法拷贝视频到本地，无法编辑", preferredStyle: .alert)
                    alert.addAction(UIAlertAction(title: "确定", style: .default))
                    present(alert, animated: true)
                    return
                }
            }
            TXUGCRecord.shareInstance().partsManager.deleteAllParts()
            let bgmInfo = selectedBGM
            let editVC = VideoEditViewController(videoPath: finalPath, asset: nil, bgm: bgmInfo)
            editVC.modalPresentationStyle = .fullScreen
            present(editVC, animated: true)
            return
        }
        // 相册视频，直接用 AVAsset
        let url = URL(fileURLWithPath: videoPath)
        let asset = AVAsset(url: url)
        print("[调试] 文件不存在，尝试用 AVAsset，isPlayable=\(asset.isPlayable)")
        let bgmInfo = selectedBGM
        let editVC = VideoEditViewController(videoPath: videoPath, asset: asset, bgm: bgmInfo)
        editVC.modalPresentationStyle = .fullScreen
        present(editVC, animated: true)
    }

    // MARK: - BeautyPanelViewDelegate
    func beautyPanelDidSelectType(_ beauty: BeautyItem) {
        print("选择美颜: \(beauty.name), ID: \(beauty.id)")
        
        // 直接判断featureCode是否为nil，无需赋值
        guard beauty.featureCode != nil else {
            // 对于"无"特效，关闭所有美颜效果
            TXUGCRecord.shareInstance().getBeautyManager().setBeautyStyle(.nature)
            TXUGCRecord.shareInstance().getBeautyManager().setBeautyLevel(0)
            TXUGCRecord.shareInstance().getBeautyManager().setWhitenessLevel(0)
            TXUGCRecord.shareInstance().getBeautyManager().setRuddyLevel(0)
            print("关闭美颜效果")
            return
        }
        
        // 应用美颜效果并设置强度
        applyBeautyEffect(for: beauty)
    }
    
    func beautyPanelDidChangeIntensity(_ intensity: Float, for beauty: BeautyItem) {
        print("美颜强度变化: \(intensity), 美颜: \(beauty.name)")
        
        // 应用美颜效果
        applyBeautyEffect(for: beauty)
    }
    
    func beautyPanelDidTapDownload(for beauty: BeautyItem) {
        print("请求下载美颜: \(beauty.name), ID: \(beauty.id)")
        // 下载逻辑已在面板内部实现
    }
    
    func beautyPanelDidTapOriginal() {
        print("显示原图")
        // 临时关闭所有美颜效果
        TXUGCRecord.shareInstance().getBeautyManager().setBeautyStyle(.nature)
        TXUGCRecord.shareInstance().getBeautyManager().setBeautyLevel(0)
        TXUGCRecord.shareInstance().getBeautyManager().setWhitenessLevel(0)
        TXUGCRecord.shareInstance().getBeautyManager().setRuddyLevel(0)
    }
    
    // MARK: - 辅助方法：应用美颜效果
    private func applyBeautyEffect(for beauty: BeautyItem) {
        guard let featureCode = beauty.featureCode else { return }
        
        let sdkValue = beauty.intensity * 9  // 将0-1的值映射到0-9范围
        
        switch featureCode {
        case "1": // 光滑
            TXUGCRecord.shareInstance().getBeautyManager().setBeautyStyle(.smooth)
            TXUGCRecord.shareInstance().getBeautyManager().setBeautyLevel(sdkValue)
            TXUGCRecord.shareInstance().getBeautyManager().setWhitenessLevel(sdkValue)
            print("应用光滑美颜效果，强度: \(sdkValue)")
        case "2": // 自然
            TXUGCRecord.shareInstance().getBeautyManager().setBeautyStyle(.nature)
            TXUGCRecord.shareInstance().getBeautyManager().setBeautyLevel(sdkValue)
            TXUGCRecord.shareInstance().getBeautyManager().setWhitenessLevel(sdkValue)
            print("应用自然美颜效果，强度: \(sdkValue)")
        case "3": // 优图
            TXUGCRecord.shareInstance().getBeautyManager().setBeautyStyle(.pitu)
            TXUGCRecord.shareInstance().getBeautyManager().setBeautyLevel(sdkValue)
            TXUGCRecord.shareInstance().getBeautyManager().setWhitenessLevel(sdkValue)
            print("应用优图美颜效果，强度: \(sdkValue)")
        default:
            print("未知的美颜效果代码: \(featureCode)")
        }
    }

    // MARK: - FilterPanelViewDelegate
    func filterPanelDidSelectFilter(_ filter: FilterItem) {
        print("选择滤镜: \(filter.name), ID: \(filter.id)")
        
        // 获取滤镜文件名
        guard let fileName = URL(string: filter.contentFile)?.lastPathComponent else {
            print("无法获取滤镜文件名")
            return
        }
        
        // 构建本地滤镜文件路径
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let filterURL = documentsDirectory.appendingPathComponent("Filters/\(fileName)")
        
        // 检查滤镜文件是否存在
        if FileManager.default.fileExists(atPath: filterURL.path) {
            // 从文件加载UIImage
            if let filterImage = UIImage(contentsOfFile: filterURL.path) {
                // 使用UIImage应用滤镜
                TXUGCRecord.shareInstance().getBeautyManager().setFilter(filterImage)
                TXUGCRecord.shareInstance().getBeautyManager().setFilterStrength(filter.intensity * 9)
                print("滤镜应用成功: \(filterURL.path)")
            } else {
                print("无法加载滤镜图像")
            }
        } else {
            print("滤镜文件不存在: \(filterURL.path)")
        }
    }
    
    func filterPanelDidTapDownload(for filter: FilterItem) {
        print("请求下载滤镜: \(filter.name), ID: \(filter.id)")
    }
    
    func filterPanelDidChangeIntensity(_ intensity: Float, for filter: FilterItem) {
        print("滤镜强度变化: \(intensity), 滤镜: \(filter.name)")
        TXUGCRecord.shareInstance().getBeautyManager().setFilterStrength(intensity)
    }
    
    // MARK: - MusicPanelViewDelegate
    func musicPanelDidSelectMusic(_ musicItem: MusicItem?) {
        // 停止当前试听
        bgmPlayer?.stop()
        bgmPlayer = nil

        if let music = musicItem, let localURL = music.localFileURL {
            do {
                // 试听前切换音频会话，确保能正常播放
                try? AVAudioSession.sharedInstance().setCategory(.playback, mode: .default)
                try? AVAudioSession.sharedInstance().setActive(true)
                let player = try AVAudioPlayer(contentsOf: localURL)
                player.numberOfLoops = 0 // 试听只播放一次
                player.prepareToPlay()
                player.play()
                bgmPlayer = player
                print("🎵 试听播放音乐: \(music.title), isPlaying: \(player.isPlaying), duration: \(player.duration), volume: \(player.volume)")
            } catch {
                print("🔴 试听音乐播放器初始化失败: \(error)")
                bgmPlayer = nil
            }
        } else {
            print("取消选中音乐，清除BGM")
        }
        // 同步底部Bar的选中状态
        setSelectedMusic(musicItem)
        // 新增：选中音乐时自动静音麦克风，取消音乐时恢复麦克风
        if musicItem != nil {
            toggleMic(true)
        } else {
            toggleMic(false)
        }
    }

    func musicPanelDidToggleFavorite(_ musicItem: MusicItem, at indexPath: IndexPath) {
        print("切换收藏状态: \(musicItem.title), 收藏: \(musicItem.isFavorite)")
        // 处理收藏状态变更逻辑
        // 例如：更新服务器或本地存储
    }

    func musicPanelDidToggleOriginalSound(_ isOn: Bool) {
        print("原声开关状态: \(isOn ? "开" : "关")")
        // 控制麦克风静音状态，isOn为true表示开启原声，false表示关闭原声
        toggleMic(!isOn) // 传入相反值，因为toggleMic参数表示是否静音
    }

    func musicPanelDidToggleMusicSound(_ isOn: Bool) {
        print("配乐开关状态: \(isOn ? "开" : "关")")
        // 控制背景音乐的播放状态
        if isOn {
            // 开启配乐 - 如果有选中的BGM，开始播放
            if let selected = selectedBGM, let localURL = selected.localFileURL {
                do {
                    let player = try AVAudioPlayer(contentsOf: localURL)
                    player.numberOfLoops = -1
                    player.prepareToPlay()
                    player.play()
                    bgmPlayer = player
                    print("🎵 开启配乐播放: \(selected.title)")
                } catch {
                    print("🔴 配乐播放器初始化失败: \(error)")
                    bgmPlayer = nil
                }
            }
        } else {
            // 关闭配乐 - 停止BGM播放
            bgmPlayer?.stop()
            bgmPlayer = nil
            print("🔇 关闭配乐播放")
        }
    }

    func musicPanelDidAdjustOriginalVolume(_ volume: Float) {
        print("调整原声音量: \(volume)")
        // 使用TXUGCRecord的音量控制API
        // volume范围是0.0-1.0，需要转换为TXUGCRecord期望的范围
        let adjustedVolume = volume * 2.0 // TXUGCRecord建议值为0~2

        // 设置麦克风音量（原声音量）
        let result = TXUGCRecord.shareInstance().setMicVolume(adjustedVolume)
        if result {
            print("✅ 成功设置麦克风音量: \(adjustedVolume)")
        } else {
            print("❌ 设置麦克风音量失败")
        }
    }

    func musicPanelDidAdjustMusicVolume(_ volume: Float) {
        print("调整配乐音量: \(volume)")
        // 使用TXUGCRecord的音量控制API
        // volume范围是0.0-1.0，需要转换为TXUGCRecord期望的范围
        let adjustedVolume = volume * 2.0 // TXUGCRecord建议值为0~2

        // 设置BGM音量
        let bgmResult = TXUGCRecord.shareInstance().setBGMVolume(adjustedVolume)
        if bgmResult {
            print("✅ 成功设置BGM音量: \(adjustedVolume)")
        } else {
            print("❌ 设置BGM音量失败")
        }

        // 同时调整AVAudioPlayer的音量（用于试听）
        bgmPlayer?.volume = volume
    }

    // MARK: - RecordSettingsPopupViewDelegate
    func recordSettingsPopupDidTapGrid(_ isSelected: Bool) {
        print("Delegate: Grid Tapped - Selected: \(isSelected)")
        UserDefaults.standard.set(isSelected, forKey: RecordSettingsKeys.gridEnabled)
        gridView.isHidden = !isSelected

        // 如果是第一次显示网格，确保网格高度正确设置
        if isSelected && !gridViewConstraintsSet {
            updateGridViewHeight(for: currentAspectRatio)
            gridViewConstraintsSet = true
        }
    }
    func recordSettingsPopupDidTapVolumeCapture(_ isSelected: Bool) {
        print("[音量键拍摄] 用户切换音量键拍摄功能: \(isSelected)")
        UserDefaults.standard.set(isSelected, forKey: RecordSettingsKeys.volumeCaptureEnabled)
        isVolumeCaptureEnabled = isSelected
        // 立即同步到管理器
        volumeKeyManager.isEnabled = isSelected
        if isSelected {
            volumeKeyManager.start()
        } else {
            volumeKeyManager.stop()
        }
    }
    func recordSettingsPopupDidChangeAspectRatio(to aspectRatio: String) {
        showAspectRatioSelector()
        UserDefaults.standard.set(aspectRatio, forKey: RecordSettingsKeys.aspectRatio)
    }
    func recordSettingsPopupDidChangeTimer(to timerValue: String) {
        showTimerSelector()
        UserDefaults.standard.set(timerValue, forKey: RecordSettingsKeys.timerValue)
    }

    // MARK: - Helper Methods
    @objc private func handleDimmingOverlayTap() {
        print("Dimming overlay tapped")
        dismissPanel()
    }

    @objc private func handlePopupMaskTap() {
        switch currentPopup {
        case .aspectRatio:
            hideAspectRatioSelector()
        case .settings:
            hideSettingsPopup()
        case .timer:
            // 读取当前选中项并同步到设置弹窗，同时保存设置
            if let selected = timerSelectorView.currentSelectedOption {
                settingsPopupView.setTimerLabelText(selected.title)
                // 保存定时设置
                currentTimerValue = selected.rawValue
                UserDefaults.standard.set(selected.title, forKey: RecordSettingsKeys.timerValue)
                print("[定时设置] 遮罩点击保存定时值: \(selected.title) (\(selected.rawValue)秒)")
            }
            hideTimerSelector()
        default:
            break
        }
    }

    private func showSettingsPopup() {
        if currentPopup == .aspectRatio {
            hideAspectRatioSelector(animated: false)
        }
        settingsPopupView.isHidden = false
        aspectRatioSelectorView.isHidden = true
        popupMaskView.isHidden = false
        currentPopup = .settings
        view.bringSubviewToFront(popupMaskView)
        view.bringSubviewToFront(settingsPopupView)
        settingsPopupView.show()
    }

    private func hideSettingsPopup() {
        settingsPopupView.hide()
        settingsPopupView.isHidden = true
        popupMaskView.isHidden = true
        currentPopup = .none
    }

    private func showAspectRatioSelector() {
        if currentPopup == .settings {
            settingsPopupView.hide()
            settingsPopupView.isHidden = true
        }
        aspectRatioSelectorView.isHidden = false
        aspectRatioSelectorView.alpha = 0
        popupMaskView.isHidden = false
        currentPopup = .aspectRatio
        view.bringSubviewToFront(popupMaskView)
        view.bringSubviewToFront(aspectRatioSelectorView)
        UIView.animate(withDuration: 0.22) {
            self.aspectRatioSelectorView.alpha = 1
        }
    }

    private func hideAspectRatioSelector(animated: Bool = true) {
        let duration = animated ? 0.18 : 0
        UIView.animate(withDuration: duration, animations: {
            self.aspectRatioSelectorView.alpha = 0
        }) { _ in
            self.aspectRatioSelectorView.isHidden = true
            self.currentPopup = .none
            // 收起后恢复设置弹窗
            self.showSettingsPopup()
        }
    }

    // MARK: - 网格高度适配
    private func updateGridViewHeight(for aspectRatio: AspectRatioOption) {
        let screenWidth = UIScreen.main.bounds.width
        var newHeight: CGFloat = 0
        switch aspectRatio {
        case .ratio1_1:
            newHeight = screenWidth
        case .ratio3_4:
            newHeight = screenWidth * 4.0 / 3.0
        case .ratio9_16:
            newHeight = screenWidth * 16.0 / 9.0
        case .ratio16_9:
            newHeight = screenWidth * 9.0 / 16.0
        }
        gridView.snp.remakeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(screenWidth)
            make.height.equalTo(newHeight)
        }
        gridView.setNeedsLayout()
        gridView.layoutIfNeeded()
    }

    // MARK: - 新增：弹窗遮罩和弹出/隐藏逻辑
    private func showTimerSelector() {
        if currentPopup == .settings {
            settingsPopupView.hide()
            settingsPopupView.isHidden = true
        }
        timerSelectorView.isHidden = false
        timerSelectorView.alpha = 0
        popupMaskView.isHidden = false
        currentPopup = .timer
        view.bringSubviewToFront(popupMaskView)
        view.bringSubviewToFront(timerSelectorView)
        UIView.animate(withDuration: 0.22) {
            self.timerSelectorView.alpha = 1
        }
    }
    private func hideTimerSelector(animated: Bool = true) {
        let duration = animated ? 0.18 : 0
        UIView.animate(withDuration: duration, animations: {
            self.timerSelectorView.alpha = 0
        }) { _ in
            self.timerSelectorView.isHidden = true
            self.currentPopup = .none
            // 收起后恢复设置弹窗
            self.showSettingsPopup()
        }
    }

    // 新增：录制设置项本地持久化Key
    private struct RecordSettingsKeys {
        static let volumeCaptureEnabled = "record_volumeCaptureEnabled"
        static let gridEnabled = "record_gridEnabled"
        static let mirrorEnabled = "record_mirrorEnabled"
        static let aspectRatio = "record_aspectRatio"
        static let timerValue = "record_timerValue"
    }

    // 新增：加载本地设置
    private func loadRecordSettings() {
        // 音量键拍摄
        var grid = false
        var volumeCapture = false
        var aspect = currentAspectRatio.title
        var timer = "关"
        if UserDefaults.standard.object(forKey: RecordSettingsKeys.volumeCaptureEnabled) != nil {
            isVolumeCaptureEnabled = UserDefaults.standard.bool(forKey: RecordSettingsKeys.volumeCaptureEnabled)
            volumeCapture = isVolumeCaptureEnabled
        }
        // 网格
        if UserDefaults.standard.object(forKey: RecordSettingsKeys.gridEnabled) != nil {
            let gridValue = UserDefaults.standard.bool(forKey: RecordSettingsKeys.gridEnabled)
            gridView.isHidden = !gridValue
            grid = gridValue
        }
        // 镜像
        if UserDefaults.standard.object(forKey: RecordSettingsKeys.mirrorEnabled) != nil {
            isMirrorEnabled = UserDefaults.standard.bool(forKey: RecordSettingsKeys.mirrorEnabled)
            mirrorButton.isSelected = isMirrorEnabled

            // 应用镜像设置到SDK
            if isMirrorEnabled {
                TXUGCRecord.shareInstance().setVideoRenderMirrorType(.VIDEO_RENDER_MIRROR_TYPE_ENABLE)
                TXUGCRecord.shareInstance().setVideoEncoderMirror(true)
            } else {
                TXUGCRecord.shareInstance().setVideoRenderMirrorType(.VIDEO_RENDER_MIRROR_TYPE_AUTO)
                TXUGCRecord.shareInstance().setVideoEncoderMirror(false)
            }
        }
        // 比例
        if let aspectRaw = UserDefaults.standard.string(forKey: RecordSettingsKeys.aspectRatio),
           let aspectOption = AspectRatioOption.from(title: aspectRaw) {
            currentAspectRatio = aspectOption
            aspectRatioSelectorView.setSelected(aspectOption)
            settingsPopupView.setAspectRatioLabelText(aspectOption.title)
            aspect = aspectOption.title
            // SDK切换比例
            let sdkRatio: TXVideoAspectRatio
            switch aspectOption {
            case .ratio1_1: sdkRatio = .VIDEO_ASPECT_RATIO_1_1
            case .ratio3_4: sdkRatio = .VIDEO_ASPECT_RATIO_3_4
            case .ratio9_16: sdkRatio = .VIDEO_ASPECT_RATIO_9_16
            case .ratio16_9: sdkRatio = .VIDEO_ASPECT_RATIO_16_9
            }
            TXUGCRecord.shareInstance().setAspectRatio(sdkRatio)
            updateGridViewHeight(for: aspectOption)
        }
        // 定时
        if let timerRaw = UserDefaults.standard.string(forKey: RecordSettingsKeys.timerValue) {
            timerSelectorView.setSelectedByTitle(timerRaw)
            settingsPopupView.setTimerLabelText(timerRaw)
            timer = timerRaw

            // 解析定时值
            if let timerOption = TimerOption.allCases.first(where: { $0.title == timerRaw }) {
                currentTimerValue = timerOption.rawValue
                print("[定时设置] 加载定时值: \(timerRaw) (\(currentTimerValue)秒)")
            }
        }
        print("[录制页] 加载本地设置 grid=\(grid) volumeCapture=\(volumeCapture) aspect=\(aspect) timer=\(timer)")
        settingsPopupView.syncState(grid: grid, volumeCapture: volumeCapture, aspectRatio: aspect, timer: timer)
    }

    // 新增：同步设置弹窗UI状态
    private func syncSettingsPopupState() {
        // 重新加载本地设置并同步到弹窗
        loadRecordSettings()
    }

    // 新增：优先从本地配置读取比例
    private func initialAspectRatioFromStorage() -> AspectRatioOption {
        if let aspectRaw = UserDefaults.standard.string(forKey: RecordSettingsKeys.aspectRatio),
           let aspect = AspectRatioOption.from(title: aspectRaw) {
            return aspect
        }
        return .ratio9_16 // 默认值
    }

    // 4. dot 跟随选中项
    private func moveDotToSelectedCell() {
        guard let cell = modeCollectionView.cellForItem(at: IndexPath(item: selectedModeIndex, section: 0)) else { return }
        let cellFrame = cell.convert(cell.bounds, to: bottomMaskContainer)
        let centerX = cellFrame.midX
        let dotY = cellFrame.midY + 12 // cell中线下方12pt
        modeDot.snp.remakeConstraints { make in
            make.centerX.equalTo(centerX)
            make.centerY.equalTo(dotY)
            make.width.height.equalTo(4)
        }
        UIView.animate(withDuration: 0.2) {
            self.view.layoutIfNeeded()
        }
    }

    // 在类内添加保存回调方法：
    @objc private func image(_ image: UIImage, didFinishSavingWithError error: Error?, contextInfo: UnsafeRawPointer) {
        if let error = error {
            let alert = UIAlertController(title: "保存失败", message: error.localizedDescription, preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
        } else {
            let alert = UIAlertController(title: "保存成功", message: "图片已保存到相册", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
        }
    }

    private func setSelectedMusic(_ music: MusicItem?) {
        if let music = music {
            musicSelectBar.setSelectedMusic(music)
            selectedBGM = music
        } else {
            musicSelectBar.setUnselected()
            selectedBGM = nil
        }
    }
}

// 3. CollectionView Delegate & DataSource
extension VideoRecordViewController: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return modeTitles.count
    }
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "ModeCell", for: indexPath) as! ModeCell
        let isSelected = (indexPath.item == selectedModeIndex)
        cell.configure(title: modeTitles[indexPath.item], selected: isSelected)
        return cell
    }
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        selectedModeIndex = indexPath.item
        collectionView.reloadData()
        collectionView.scrollToItem(at: indexPath, at: .centeredHorizontally, animated: true)
        moveDotToSelectedCell()
        // 打印点击事件
        let mode = modeTitles[indexPath.item]
        print("点击了模式：\(mode)")
        // 切换录制按钮图片
        if selectedModeIndex == 0 { // 笔记
            recordButton.setImage(UIImage(named: "record_notes_play"), for: .normal)
            recordButton.setImage(nil, for: .selected)
        } else { // 视频
            recordButton.setImage(UIImage(named: "record_shoot_play"), for: .normal)
            recordButton.setImage(UIImage(named: "record_shoot_stop"), for: .selected)
        }
        // 可在这里做对应UI调整
    }
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let text = modeTitles[indexPath.item]
        let font = indexPath.item == selectedModeIndex ? UIFont.boldSystemFont(ofSize: 20) : UIFont.systemFont(ofSize: 16)
        let width = (text as NSString).size(withAttributes: [.font: font]).width + 32
        return CGSize(width: width, height: 44)
    }
}

extension VideoRecordViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        picker.dismiss(animated: true, completion: nil)
        if let image = info[.originalImage] as? UIImage {
            print("获取到图片: \(image)")
            if selectedModeIndex == 0 {
                let notePublishVC = NoteEditingDetailsViewController(images: [image])
                navigationController?.pushViewController(notePublishVC, animated: true)
            }
        }
        if let videoURL = info[.mediaURL] as? URL {
            print("[调试] 获取到视频: \(videoURL)")
            print("[调试] 文件存在: \(FileManager.default.fileExists(atPath: videoURL.path))")
            goToEditPage(videoPath: videoURL.path)
        }
    }
} 
