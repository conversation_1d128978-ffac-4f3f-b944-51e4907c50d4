//
//  BeautyPanelView.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/4/21.
//

import UIKit
import SnapKit

// 美颜面板代理
protocol BeautyPanelViewDelegate: AnyObject {
    func beautyPanelDidSelectType(_ beauty: BeautyItem)
    func beautyPanelDidChangeIntensity(_ intensity: Float, for beauty: BeautyItem)
    func beautyPanelDidTapDownload(for beauty: BeautyItem)
    func beautyPanelDidTapOriginal()
}

// 自定义滑块，增加点击范围
class CustomSlider: UISlider {
    override func point(inside point: CGPoint, with event: UIEvent?) -> Bool {
        // 增加点击范围：上下各增加15点，左右各增加10点
        let expandedBounds = bounds.insetBy(dx: -10, dy: -15)
        return expandedBounds.contains(point)
    }

    override func thumbRect(forBounds bounds: CGRect, trackRect rect: CGRect, value: Float) -> CGRect {
        let thumbRect = super.thumbRect(forBounds: bounds, trackRect: rect, value: value)
        // 增加拖动按钮的实际触摸区域
        return thumbRect.insetBy(dx: -8, dy: -8)
    }
}

class BeautyPanelView: UIView {

    weak var delegate: BeautyPanelViewDelegate?

    // MARK: - Properties
    private var beautyItems: [BeautyItem] = []
    private var selectedBeautyId: Int?
    private var currentItemIndex: Int = 0
    private var originalIntensities: [Int: Float] = [:] // 保存原始美颜强度

    // MARK: - UI Components
    private lazy var blurView: UIVisualEffectView = {
        let blurEffect = UIBlurEffect(style: .dark)
        let view = UIVisualEffectView(effect: blurEffect)
        return view
    }()

    private lazy var topContainerView = UIView() // 顶部容器（滑块区域）

    private lazy var resetButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(systemName: "arrow.counterclockwise"), for: .normal)
        button.tintColor = UIColor.white
        button.addTarget(self, action: #selector(resetButtonTapped), for: .touchUpInside)
        return button
    }()

    private lazy var intensitySlider: CustomSlider = {
        let slider = CustomSlider()
        slider.minimumValue = 0
        slider.maximumValue = 1
        slider.minimumTrackTintColor = UIColor.white
        slider.maximumTrackTintColor = UIColor.white.withAlphaComponent(0.3)
        slider.setThumbImage(createThumbImage(size: CGSize(width: 16, height: 16)), for: .normal) // 增大拖动按钮
        slider.addTarget(self, action: #selector(sliderValueChanged(_:)), for: .valueChanged)
        slider.addTarget(self, action: #selector(sliderTouchEnded), for: [.touchUpInside, .touchUpOutside])
        return slider
    }()

    private lazy var valueLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor.white
        label.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        label.textAlignment = .center
        label.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        label.layer.cornerRadius = 8
        label.layer.masksToBounds = true
        label.isHidden = true
        return label
    }()

    private lazy var referenceMarkView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.white.withAlphaComponent(0.7)
        view.layer.cornerRadius = 3
        view.isHidden = true
        return view
    }()

    private lazy var originalLabel: UILabel = {
        let label = UILabel()
        label.text = "原图"
        label.textColor = UIColor.white.withAlphaComponent(0.7)
        label.font = UIFont.systemFont(ofSize: 13)
        label.textAlignment = .center
        label.isUserInteractionEnabled = true

        // 添加长按手势替代点击手势
        let longPressGesture = UILongPressGestureRecognizer(target: self, action: #selector(originalLabelLongPressed(_:)))
        longPressGesture.minimumPressDuration = 0.1 // 设置较短的长按时间
        label.addGestureRecognizer(longPressGesture)

        return label
    }()

    // 底部容器（包含美颜选项）
    private lazy var bottomContainerView: UIView = {
        let view = UIView()
        return view
    }()

    // 美颜水平滚动视图
    private lazy var beautyScrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.contentInset = UIEdgeInsets(top: 0, left: 20, bottom: 0, right: 20)
        return scrollView
    }()

    // 美颜内容容器视图
    private lazy var beautyContentView: UIView = {
        let view = UIView()
        return view
    }()

    // 加载指示器
    private lazy var loadingIndicator: UIActivityIndicatorView = {
        let indicator = UIActivityIndicatorView(style: .medium)
        indicator.color = UIColor.white
        indicator.hidesWhenStopped = true
        return indicator
    }()

    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        fetchBeautyList()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        fetchBeautyList()
    }

    // MARK: - Setup UI
    private func setupUI() {
        backgroundColor = UIColor.black.withAlphaComponent(0.9) // 增加不透明度，让背景更黑

        // 移除模糊背景视图，不再添加到视图层级中（移除透明蒙版效果）
        // addSubview(blurView)
        addSubview(topContainerView)
        topContainerView.addSubview(resetButton)
        topContainerView.addSubview(intensitySlider)
        topContainerView.addSubview(originalLabel)
        
        addSubview(valueLabel)
        addSubview(referenceMarkView)
        
        addSubview(bottomContainerView)
        bottomContainerView.addSubview(beautyScrollView)
        beautyScrollView.addSubview(beautyContentView)
        addSubview(loadingIndicator)

        // 布局
        // 移除模糊视图约束
        // blurView.snp.makeConstraints { $0.edges.equalToSuperview() }

        topContainerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(10)
            make.leading.trailing.equalToSuperview().inset(15)
            make.height.equalTo(40)
        }

        resetButton.snp.makeConstraints { make in
            make.leading.equalToSuperview()
            make.centerY.equalToSuperview()
            make.size.equalTo(30)
        }

        intensitySlider.snp.makeConstraints { make in
            make.leading.equalTo(resetButton.snp.trailing).offset(10)
            make.trailing.equalTo(originalLabel.snp.leading).offset(-10)
            make.centerY.equalToSuperview()
        }

        originalLabel.snp.makeConstraints { make in
            make.trailing.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalTo(40)
        }

        valueLabel.snp.makeConstraints { make in
            make.centerX.equalTo(intensitySlider)
            make.bottom.equalTo(intensitySlider.snp.top).offset(-5)
            make.width.greaterThanOrEqualTo(30)
            make.height.equalTo(20)
        }

        bottomContainerView.snp.makeConstraints { make in
            make.top.equalTo(topContainerView.snp.bottom).offset(15)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalTo(self.safeAreaLayoutGuide.snp.bottom).inset(15)
        }

        beautyScrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        beautyContentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(beautyScrollView)
        }

        loadingIndicator.snp.makeConstraints { make in
            make.center.equalTo(bottomContainerView)
        }

        // 确保valueLabel在最顶层
        bringSubviewToFront(valueLabel)

        // 使用自定义CustomSlider，已经有更大的点击范围，不需要额外的点击手势
        // let tapGesture = UITapGestureRecognizer(target: self, action: #selector(sliderTapped(_:)))
        // intensitySlider.addGestureRecognizer(tapGesture)
    }

    // 创建滑块的圆形图像
    private func createThumbImage(size: CGSize) -> UIImage {
        UIGraphicsBeginImageContextWithOptions(size, false, 0.0)
        
        let context = UIGraphicsGetCurrentContext()
        context?.setFillColor(UIColor.white.cgColor)
        
        let rect = CGRect(x: 0, y: 0, width: size.width, height: size.height)
        context?.fillEllipse(in: rect)
        
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return image ?? UIImage()
    }

    // MARK: - Data Loading
    
    func fetchBeautyList() {
        loadingIndicator.startAnimating()
        
        APIManager.shared.getFilterList(type: "2") { [weak self] result in
            guard let self = self else { return }
            
            self.loadingIndicator.stopAnimating()
            
            switch result {
            case .success(let response):
                if let data = response.data {
                    self.processBeautyData(data)
                    // 预加载所有美颜图标
                    self.preloadAllBeautyIcons()
                } else {
                    print("美颜数据为空")
                }
            case .failure(let error):
                print("获取美颜列表失败: \(error.localizedDescription)")
            }
        }
    }
    
    // 预加载所有美颜图标
    private func preloadAllBeautyIcons() {
        print("开始预加载所有美颜图标，共\(beautyItems.count)个")
        
        // 创建缓存目录
        let cachesDirectory = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first!
        let beautyIconsDirectory = cachesDirectory.appendingPathComponent("BeautyIcons")
        
        do {
            if !FileManager.default.fileExists(atPath: beautyIconsDirectory.path) {
                try FileManager.default.createDirectory(at: beautyIconsDirectory, withIntermediateDirectories: true, attributes: nil)
                print("创建美颜图标缓存目录成功: \(beautyIconsDirectory.path)")
            }
        } catch {
            print("创建美颜图标缓存目录失败: \(error)")
        }
        
        // 优先下载第一个美颜的选中状态图标（如果有）
        if !beautyItems.isEmpty {
            let firstItem = beautyItems[0]
            
            // 下载选中状态图标(优先)
            if let imageURL = URL(string: firstItem.selectedImageURL) {
                let identifier = "\(firstItem.id)_selected"
                print("优先下载第一个美颜选中图标: \(identifier)")

                downloadImage(from: imageURL, identifier: identifier) { [weak self] image in
                    if let image = image {
                        print("第一个美颜选中图标下载成功")
                        // 确保界面更新
                        DispatchQueue.main.async {
                            guard let self = self else { return }
                            if let containerView = self.beautyContentView.viewWithTag(100),
                               let button = containerView.viewWithTag(200) as? UIButton {
                                button.setImage(image, for: .normal)
                            }
                        }
                    }
                }
            }

            // 下载未选中状态图标
            if let imageURL = URL(string: firstItem.normalImageURL) {
                let identifier = "\(firstItem.id)_normal"
                downloadImage(from: imageURL, identifier: identifier)
            }
        }
        
        // 延迟下载其他美颜图标
        DispatchQueue.global().async { [weak self] in
            guard let self = self else { return }
            
            // 下载其他美颜图标
            for (index, item) in self.beautyItems.enumerated() {
                if index == 0 { continue } // 跳过第一个，因为已经优先下载了

                // 下载未选中状态图标
                if let imageURL = URL(string: item.normalImageURL) {
                    self.downloadImage(from: imageURL, identifier: "\(item.id)_normal")
                }

                // 下载选中状态图标
                if let imageURL = URL(string: item.selectedImageURL) {
                    self.downloadImage(from: imageURL, identifier: "\(item.id)_selected")
                }
            }
        }
    }
    
    // 下载并缓存图片
    private func downloadImage(from url: URL, identifier: String, completion: ((UIImage?) -> Void)? = nil) {
        // 检查本地缓存
        if let cachedImage = loadImageFromCache(identifier: identifier) {
            print("图片已缓存: \(identifier)")
            completion?(cachedImage)
            return
        }
        
        // 下载图片
        let task = URLSession.shared.dataTask(with: url) { [weak self] data, response, error in
            guard let self = self, let data = data, error == nil else {
                print("下载图片失败: \(url.absoluteString), 错误: \(error?.localizedDescription ?? "未知错误")")
                DispatchQueue.main.async {
                    completion?(nil)
                }
                return
            }
            
            // 保存到缓存
            self.saveImageToCache(imageData: data, identifier: identifier)
            
            // 加载保存的图片并回调
            if let image = self.loadImageFromCache(identifier: identifier) {
                DispatchQueue.main.async {
                    completion?(image)
                }
            } else {
                DispatchQueue.main.async {
                    completion?(nil)
                }
            }
        }
        
        task.resume()
    }
    
    // 保存图片到缓存
    private func saveImageToCache(imageData: Data, identifier: String) {
        let cachesDirectory = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first!
        let beautyIconsDirectory = cachesDirectory.appendingPathComponent("BeautyIcons")
        let fileURL = beautyIconsDirectory.appendingPathComponent("\(identifier).png")
        
        do {
            try imageData.write(to: fileURL)
            print("图片缓存成功: \(identifier)")
        } catch {
            print("保存图片到缓存失败: \(identifier), 错误: \(error)")
        }
    }
    
    // 从缓存加载图片
    private func loadImageFromCache(identifier: String) -> UIImage? {
        let cachesDirectory = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first!
        let beautyIconsDirectory = cachesDirectory.appendingPathComponent("BeautyIcons")
        let fileURL = beautyIconsDirectory.appendingPathComponent("\(identifier).png")
        
        if FileManager.default.fileExists(atPath: fileURL.path) {
            return UIImage(contentsOfFile: fileURL.path)
        }
        
        return nil
    }
    
    private func processBeautyData(_ beautyData: [VideoEditConfigDataItem]) {
        // 转换API数据到BeautyItem数组
        beautyItems = beautyData.map { item in
            return BeautyItem(
                id: item.id!,
                name: item.name!,
                featureCode: item.featureCode,
                describeImg: item.describeImg ?? "",
                contentFile: item.contentFile ?? "",
                checkedOfBeauty: item.checkedOfBeauty,
                uncheckedOfBeauty: item.uncheckedOfBeauty,
                isSelected: false
            )
        }

        // 如果有美颜项，默认选中第一个
        if !beautyItems.isEmpty {
            beautyItems[0].isSelected = true
            selectedBeautyId = beautyItems[0].id
            updateSliderValue()

            // 预加载第一个美颜的图标（选中状态）
            if let imageURL = URL(string: beautyItems[0].selectedImageURL) {
                let identifier = "\(beautyItems[0].id)_selected"
                downloadImage(from: imageURL, identifier: identifier) { [weak self] image in
                    // 确保第一个美颜按钮立即显示正确的选中图标
                    DispatchQueue.main.async {
                        guard let self = self else { return }
                        if let containerView = self.beautyContentView.viewWithTag(100),
                           let button = containerView.viewWithTag(200) as? UIButton,
                           let image = image {
                            button.setImage(image, for: .normal)
                        }
                    }
                }
            }
        }

        // 创建美颜按钮
        createBeautyButtons()
    }

    // 创建美颜按钮
    private func createBeautyButtons() {
        // 清除现有的按钮
        for subview in beautyContentView.subviews {
            subview.removeFromSuperview()
        }
        
        guard !beautyItems.isEmpty else { return }
        
        // 按钮尺寸
        let buttonSize: CGFloat = 60
        let spacing: CGFloat = 15
        let labelHeight: CGFloat = 20
        let containerHeight = buttonSize + spacing + labelHeight
        
        // 计算内容宽度
        let contentWidth = CGFloat(beautyItems.count) * (buttonSize + spacing) + spacing
        beautyContentView.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(beautyScrollView)
            make.width.equalTo(contentWidth)
        }
        
        // 创建美颜按钮
        for (index, item) in beautyItems.enumerated() {
            createBeautyButton(for: item, at: index, buttonSize: buttonSize, spacing: spacing, labelHeight: labelHeight, containerHeight: containerHeight)
        }
    }

    // 创建单个美颜按钮
    private func createBeautyButton(for item: BeautyItem, at index: Int, buttonSize: CGFloat, spacing: CGFloat, labelHeight: CGFloat, containerHeight: CGFloat) {
        // 创建容器视图
        let itemContainer = UIView()
        itemContainer.tag = 100 + index
        beautyContentView.addSubview(itemContainer)
        
        // 设置容器位置
        itemContainer.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(spacing + CGFloat(index) * (buttonSize + spacing))
            make.top.equalToSuperview()
            make.width.equalTo(buttonSize)
            make.height.equalTo(containerHeight)
        }
        
        // 创建美颜缩略图按钮
        let button = UIButton(type: .custom)
        button.tag = 200 + index
        button.layer.cornerRadius = 12
        button.clipsToBounds = true
        button.layer.borderWidth = item.isSelected ? 2 : 0
        button.layer.borderColor = UIColor(hex: "#FF8F1F").cgColor
        button.addTarget(self, action: #selector(beautyButtonTapped(_:)), for: .touchUpInside)
        button.contentMode = .center
        button.imageView?.contentMode = .scaleAspectFit
        
        // 设置图片内边距，使25*25的图片在60*60按钮中居中显示
        let padding: CGFloat = (buttonSize - 25) / 2
        button.imageEdgeInsets = UIEdgeInsets(top: padding, left: padding, bottom: padding, right: padding)
        
        itemContainer.addSubview(button)
        
        // 设置美颜缩略图
        let imageURL = item.isSelected ? item.selectedImageURL : item.normalImageURL
        if let url = URL(string: imageURL) {
            // 根据选中状态确定标识符
            let identifier = "\(item.id)_\(item.isSelected ? "selected" : "normal")"

            // 先尝试从缓存加载
            if let cachedImage = loadImageFromCache(identifier: identifier) {
                button.setImage(cachedImage, for: .normal)
            } else {
                // 如果缓存中没有，设置默认图片并启动下载
                button.setImage(UIImage(named: "filter_placeholder"), for: .normal)
                downloadImage(from: url, identifier: identifier) { [weak button] image in
                    if let image = image {
                        button?.setImage(image, for: .normal)
                    }
                }
            }
        }
        
        // 设置按钮位置
        button.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(buttonSize)
        }
        
        // 创建美颜名称标签
        let label = UILabel()
        label.tag = 300 + index
        label.text = item.name
        label.textColor = item.isSelected ? UIColor.white : UIColor.white.withAlphaComponent(0.7)
        label.font = item.isSelected ? UIFont.systemFont(ofSize: 12, weight: .medium) : UIFont.systemFont(ofSize: 12)
        label.textAlignment = .center
        itemContainer.addSubview(label)
        
        // 设置标签位置
        label.snp.makeConstraints { make in
            make.top.equalTo(button.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(labelHeight)
        }
    }

    // 更新美颜按钮状态
    private func updateBeautyButtonState(item: BeautyItem, at index: Int) {
        guard let containerView = beautyContentView.viewWithTag(100 + index),
              let button = containerView.viewWithTag(200 + index) as? UIButton,
              let label = containerView.viewWithTag(300 + index) as? UILabel else {
            return
        }
        
        // 更新按钮边框
        button.layer.borderWidth = item.isSelected ? 2 : 0
        
        // 更新按钮图标为选中/未选中状态
        let imageURL = item.isSelected ? item.selectedImageURL : item.normalImageURL
        if let url = URL(string: imageURL) {
            // 根据选中状态确定标识符
            let identifier = "\(item.id)_\(item.isSelected ? "selected" : "normal")"

            // 先尝试从缓存加载
            if let cachedImage = loadImageFromCache(identifier: identifier) {
                button.setImage(cachedImage, for: .normal)
            } else {
                // 如果缓存中没有，设置默认图片并启动下载
                button.setImage(UIImage(named: "filter_placeholder"), for: .normal)
                downloadImage(from: url, identifier: identifier) { [weak button] image in
                    if let image = image {
                        button?.setImage(image, for: .normal)
                    }
                }
            }
        }
        
        // 更新标签
        label.textColor = item.isSelected ? UIColor.white : UIColor.white.withAlphaComponent(0.7)
        label.font = item.isSelected ? UIFont.systemFont(ofSize: 12, weight: .medium) : UIFont.systemFont(ofSize: 12)
    }

    // 更新所有美颜按钮状态
    private func updateAllBeautyButtonsState() {
        for (index, item) in beautyItems.enumerated() {
            updateBeautyButtonState(item: item, at: index)
        }
    }

    // 更新滑块值和状态
    private func updateSliderValue() {
        guard let selectedId = selectedBeautyId,
              let index = beautyItems.firstIndex(where: { $0.id == selectedId }) else {
            return
        }

        let selectedItem = beautyItems[index]
        intensitySlider.value = selectedItem.intensity

        // 检查是否是"无"美颜（通常是第一个或名称包含"无"）
        let isNoneBeauty = (index == 0 && selectedItem.name.contains("无")) || selectedItem.name == "无"

        // 如果是"无"美颜，禁用滑块；否则启用
        intensitySlider.isEnabled = !isNoneBeauty
        intensitySlider.alpha = isNoneBeauty ? 0.3 : 1.0

        // 如果是"无"美颜，将强度设为0
        if isNoneBeauty {
            beautyItems[index].intensity = 0
            intensitySlider.value = 0
        }
    }

    // 应用美颜效果
    private func applyBeauty(at index: Int) {
        guard index < beautyItems.count else { return }

        // 重置所有美颜选中状态
        for i in 0..<beautyItems.count {
            beautyItems[i].isSelected = (i == index)
        }

        selectedBeautyId = beautyItems[index].id
        currentItemIndex = index

        // 特殊处理：如果选择"无"（第一个美颜项），清除所有美颜效果
        if index == 0 {
            // 清除所有美颜的强度设置
            for i in 0..<beautyItems.count {
                beautyItems[i].intensity = 0
            }
            // 清除原始强度记录
            originalIntensities.removeAll()
            // 通知代理清除所有美颜效果
            delegate?.beautyPanelDidTapOriginal()
        }

        // 更新UI
        updateAllBeautyButtonsState()
        updateSliderValue()

        // 通知代理
        delegate?.beautyPanelDidSelectType(beautyItems[index])
    }

    // MARK: - Actions
    
    @objc private func resetButtonTapped() {
        guard let selectedId = selectedBeautyId,
              let index = beautyItems.firstIndex(where: { $0.id == selectedId }) else {
            return
        }

        // 重置强度为0
        beautyItems[index].intensity = 0
        intensitySlider.value = 0

        // 更新原始强度记录
        originalIntensities[selectedId] = 0

        // 通知代理
        delegate?.beautyPanelDidChangeIntensity(0, for: beautyItems[index])
    }
    
    @objc private func sliderValueChanged(_ sender: UISlider) {
        guard let selectedId = selectedBeautyId,
              let index = beautyItems.firstIndex(where: { $0.id == selectedId }) else {
            return
        }

        let selectedItem = beautyItems[index]

        // 检查是否是"无"美颜，如果是则不允许调节
        let isNoneBeauty = (index == 0 && selectedItem.name.contains("无")) || selectedItem.name == "无"
        if isNoneBeauty {
            sender.value = 0
            return
        }

        // 更新强度值
        let value = sender.value
        beautyItems[index].intensity = value

        // 保存到原始强度记录中
        originalIntensities[selectedItem.id] = value

        // 更新值标签
        updateValueLabel(value, for: sender)

        // 通知代理
        delegate?.beautyPanelDidChangeIntensity(value, for: beautyItems[index])

        // 延迟隐藏值标签
        NSObject.cancelPreviousPerformRequests(withTarget: self, selector: #selector(hideValueLabel), object: nil)
        perform(#selector(hideValueLabel), with: nil, afterDelay: 1.0)
    }
    
    @objc private func sliderTouchEnded() {
        // 滑动结束后延迟隐藏值标签
        NSObject.cancelPreviousPerformRequests(withTarget: self, selector: #selector(hideValueLabel), object: nil)
        perform(#selector(hideValueLabel), with: nil, afterDelay: 0.5)
    }
    
    // 保存当前所有美颜的强度
    private func saveOriginalIntensities() {
        originalIntensities.removeAll()
        for item in beautyItems {
            originalIntensities[item.id] = item.intensity
        }
    }

    // 恢复所有美颜的强度
    private func restoreOriginalIntensities() {
        for i in 0..<beautyItems.count {
            if let originalIntensity = originalIntensities[beautyItems[i].id] {
                beautyItems[i].intensity = originalIntensity
            }
        }

        // 更新当前选中美颜的滑块值
        if let selectedId = selectedBeautyId,
           let index = beautyItems.firstIndex(where: { $0.id == selectedId }) {
            intensitySlider.value = beautyItems[index].intensity
        }
    }

    // 清理所有美颜强度为0
    private func clearAllBeautyIntensities() {
        for i in 0..<beautyItems.count {
            beautyItems[i].intensity = 0
        }

        // 更新当前选中美颜的滑块值
        if let selectedId = selectedBeautyId,
           let index = beautyItems.firstIndex(where: { $0.id == selectedId }) {
            intensitySlider.value = 0
        }
    }

    @objc private func originalLabelLongPressed(_ gesture: UILongPressGestureRecognizer) {
        switch gesture.state {
        case .began:
            // 按住开始：保存当前强度并清理所有美颜
            saveOriginalIntensities()
            clearAllBeautyIntensities()

            // 通知代理应用原图效果
            delegate?.beautyPanelDidTapOriginal()

        case .ended, .cancelled:
            // 松开：恢复之前保存的美颜强度
            restoreOriginalIntensities()

            // 通知代理恢复美颜效果
            if let selectedId = selectedBeautyId,
               let index = beautyItems.firstIndex(where: { $0.id == selectedId }) {
                delegate?.beautyPanelDidChangeIntensity(beautyItems[index].intensity, for: beautyItems[index])
            }

        default:
            break
        }
    }
    
    @objc private func beautyButtonTapped(_ sender: UIButton) {
        let index = sender.tag - 200
        
        guard index >= 0, index < beautyItems.count else { return }
        
        // 应用美颜
        applyBeauty(at: index)
    }
    
    @objc private func hideValueLabel() {
        valueLabel.isHidden = true
    }
    
    // 更新值标签
    private func updateValueLabel(_ value: Float, for slider: UISlider) {
        let intValue = Int(value * 100) // 转为0-100整数
        valueLabel.text = "\(intValue)"
        valueLabel.isHidden = false
        
        // 更新标签位置使其在滑块拇指上方
        let trackRect = slider.trackRect(forBounds: slider.bounds)
        let thumbRect = slider.thumbRect(forBounds: slider.bounds, trackRect: trackRect, value: value)
        let thumbCenterX = thumbRect.midX
        
        valueLabel.snp.remakeConstraints { make in
            make.centerX.equalTo(slider.snp.leading).offset(thumbCenterX)
            make.bottom.equalTo(slider.snp.top).offset(-5)
            make.width.greaterThanOrEqualTo(30)
            make.height.equalTo(20)
        }
        
        // 立即布局更新位置
        self.layoutIfNeeded()
    }
    
    private func containerViewForIndex(_ index: Int) -> UIView? {
        return beautyContentView.viewWithTag(100 + index)
    }

    // 新增：滑块点击事件，支持点击进度条直接跳转
    @objc private func sliderTapped(_ gesture: UITapGestureRecognizer) {
        // 如果滑块被禁用，不响应点击
        if !intensitySlider.isEnabled {
            return
        }

        let slider = intensitySlider
        let point = gesture.location(in: slider)
        let width = slider.bounds.width
        let minValue = slider.minimumValue
        let maxValue = slider.maximumValue
        // 计算点击位置对应的值
        let value = minValue + Float(point.x / width) * (maxValue - minValue)
        slider.setValue(value, animated: true)
        // 触发滑块值变化逻辑
        sliderValueChanged(slider)
    }
}

