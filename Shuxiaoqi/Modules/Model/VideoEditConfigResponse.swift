//
//  VideoEditConfigResponse.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yongsheng ye on 2025/4/30.
//

import SmartCodable

// MARK: - VideoEditConfigDataItem
struct VideoEditConfigDataItem: SmartCodable {
    var id: Int?
    var type: Int?
    var name: String?
    var featureCode: String?
    var musicAuthor: String?
    var duration: Int?
    var info: String? // Assuming info is a String, adjust if needed
    var describeImg: String?
    var contentFile: String? = ""
    var checkedOfBeauty: String? // 选中状态图标
    var uncheckedOfBeauty: String? // 未选中状态图标
}

struct VideoEditConfigResponse: SmartCodable {
    var status: Int = 0
    var errMsg: String?
    var msg: String?
    var data: [VideoEditConfigDataItem]? // data 是一个 UserNoticeSettingGroup 数组

    // 计算属性：判断请求是否成功
    var isSuccess: Bool {
        return status == 200
    }

    // 计算属性：获取用于显示的消息
    public var displayMessage: String {
        if let message = msg, !message.isEmpty {
            return message
        }
        if let errorMsg = errMsg, !errorMsg.isEmpty {
            return errorMsg
        }
        return isSuccess ? "成功" : "未知错误"
    }
}
