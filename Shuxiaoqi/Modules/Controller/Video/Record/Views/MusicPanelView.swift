//
//  MusicPanelView.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/5/14.
//

import Foundation
import UIKit
import SnapKit

// 委托协议，用于与VideoRecordViewController通信
protocol MusicPanelViewDelegate: AnyObject {
    func musicPanelDidSelectMusic(_ musicItem: MusicItem?)
    func musicPanelDidToggleFavorite(_ musicItem: MusicItem, at indexPath: IndexPath)
    func musicPanelDidToggleOriginalSound(_ isOn: Bool)
    func musicPanelDidToggleMusicSound(_ isOn: Bool)
    func musicPanelDidAdjustOriginalVolume(_ volume: Float)
    func musicPanelDidAdjustMusicVolume(_ volume: Float)
}

// 添加自定义竖直音量控制器类
class VerticalVolumeControl: UIView {
    private var volumeValue: Float = 50
    private var maxVolume: Float = 200
    private var isAdjusting = false
    
    private let backgroundBar = UIView()
    private let volumeBar = UIView()
    let iconImageView = UIImageView()
    let titleLabel = UILabel()
    
    var onVolumeChanged: ((Float) -> Void)?
    
    init(title: String, icon: UIImage?, initialVolume: Float) {
        super.init(frame: .zero)
        self.volumeValue = initialVolume
        setupUI(title: title, icon: icon)
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    
    private func setupUI(title: String, icon: UIImage?) {
        // 背景柱
        backgroundBar.backgroundColor = UIColor.lightGray.withAlphaComponent(0.3)
        backgroundBar.layer.cornerRadius = 25 / 2 // 柱体宽度一半
        backgroundBar.layer.masksToBounds = true
        addSubview(backgroundBar)
        
        // 音量柱
        volumeBar.backgroundColor = UIColor(hex: "#FF8F1F")
        volumeBar.layer.cornerRadius = 25 / 2 // 柱体宽度一半
        volumeBar.layer.masksToBounds = true
        addSubview(volumeBar)
        
        // 图标
        iconImageView.image = icon
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.tintColor = .white
        addSubview(iconImageView)
        
        // 标题
        titleLabel.text = "\(title)\(Int(volumeValue))"
        titleLabel.textColor = .black
        titleLabel.font = .systemFont(ofSize: 14)
        titleLabel.textAlignment = .center
        addSubview(titleLabel)
        
        // 设置约束
        backgroundBar.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.width.equalTo(50)
            make.height.equalTo(200)
            make.top.equalToSuperview()
        }
        
        updateVolumeBar()
        
        iconImageView.snp.makeConstraints { make in
            make.centerX.equalTo(volumeBar)
            make.bottom.equalTo(volumeBar).offset(-11)
            make.width.height.equalTo(28)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(backgroundBar.snp.bottom).offset(8)
        }
        
        // 添加手势
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        addGestureRecognizer(panGesture)
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        addGestureRecognizer(tapGesture)
    }
    
    private func updateVolumeBar() {
        // 计算音量柱高度比例
        let heightRatio = CGFloat(volumeValue / maxVolume)
        
        volumeBar.snp.remakeConstraints { make in
            make.centerX.equalToSuperview()
            make.width.equalTo(50)
            make.height.equalTo(backgroundBar.snp.height).multipliedBy(heightRatio)
            make.bottom.equalTo(backgroundBar.snp.bottom)
        }
    }
    
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        let touchPoint = gesture.location(in: backgroundBar)
        adjustVolume(at: touchPoint.y)
        
        if gesture.state == .ended {
            isAdjusting = false
        } else {
            isAdjusting = true
        }
    }
    
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        let touchPoint = gesture.location(in: backgroundBar)
        adjustVolume(at: touchPoint.y)
    }
    
    private func adjustVolume(at yPosition: CGFloat) {
        // 计算新的音量值（将y坐标转换为音量）
        let barHeight = backgroundBar.bounds.height
        let cappedY = min(max(0, yPosition), barHeight)
        let newVolumeRatio = 1 - (cappedY / barHeight)
        let newVolume = Float(newVolumeRatio) * maxVolume
        
        setVolume(newVolume)
    }
    
    func setVolume(_ volume: Float) {
        // 更新音量值
        volumeValue = min(max(0, volume), maxVolume)
        
        // 更新UI
        updateVolumeBar()
        
        // 提取标题前缀（如"原声"或"配乐"）
        let titlePrefix = titleLabel.text?.replacingOccurrences(of: "\\d+", with: "", options: .regularExpression) ?? ""
        titleLabel.text = "\(titlePrefix)\(Int(volumeValue))"
        
        // 通知回调
        onVolumeChanged?(volumeValue)
    }
}

class MusicPanelView: UIView {
    
    // MARK: - Public Properties
    weak var delegate: MusicPanelViewDelegate?
    // 新增：记录当前选中的BGM
    private var selectedMusic: MusicItem?
    var musicItems: [MusicItem] = [] {
        didSet {
            print("MusicPanelView - musicItems.didSet called. New count: \(musicItems.count). Reloading table.") // DEBUG
            updateSelectedMusicState() // 自动同步选中状态
        }
    }
    
    // MARK: - Private Properties
    private var currentMusicPage = 0
    private var isLoadingMusic = false
    private var canLoadMoreMusic = true
    private let musicPageSize = 10
    private var isVolumePopupShowing = false
    private var bottomSafeAreaInset: CGFloat = 0 // 保存底部安全区域的高度
    private var originalSoundVolume: Float = 100
    private var musicVolume: Float = 100

    // 搜索相关属性
    private var currentSearchKeywords: String?
    private var searchTimer: Timer?
    private var didSetupConstraints = false
    
    // MARK: - UI Components
    private lazy var musicSearchTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "搜索"
        textField.borderStyle = .roundedRect
        textField.backgroundColor = UIColor(hex: "#EEEEEE")
        textField.font = .systemFont(ofSize: 14)
        
        // 创建搜索图标视图
        let searchIconView = UIImageView(image: UIImage(named: "icon_search") ?? UIImage(systemName: "magnifyingglass"))
        searchIconView.contentMode = .scaleAspectFit
        searchIconView.tintColor = .gray // 设置图标颜色
        searchIconView.frame = CGRect(x: 0, y: 0, width: 20, height: 20)
        
        // 创建一个容器视图来包含图标和设置合适的间距
        let containerView = UIView(frame: CGRect(x: 0, y: 0, width: 8 + 20, height: 20))
        containerView.addSubview(searchIconView)
        searchIconView.center.y = containerView.bounds.midY
        searchIconView.frame.origin.x = 8 // 左边距10pt
        // 设置为左视图
        textField.leftView = containerView
        textField.leftViewMode = .always

        // 设置搜索功能
        textField.returnKeyType = .search // 设置键盘搜索按钮
        textField.delegate = self // 设置委托
        textField.addTarget(self, action: #selector(searchTextFieldDidChange(_:)), for: .editingChanged)

        return textField
    }()
    
    private lazy var allMusicSegmentButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("全部", for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        button.setTitleColor(.black, for: .selected)
        button.setTitleColor(UIColor.gray, for: .normal)
        button.addTarget(self, action: #selector(musicSegmentButtonTapped(_:)), for: .touchUpInside)
        button.isSelected = true // Default selected
        return button
    }()
    
    private lazy var favoriteMusicSegmentButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("收藏", for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        button.setTitleColor(.black, for: .selected)
        button.setTitleColor(UIColor.gray, for: .normal)
        button.addTarget(self, action: #selector(musicSegmentButtonTapped(_:)), for: .touchUpInside)
        return button
    }()
    
    private lazy var musicSegmentIndicatorView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#FF7A45")
        return view
    }()
    
    lazy var musicListTableView: UITableView = {
        let tableView = UITableView()
        tableView.backgroundColor = .clear
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(MusicListTableViewCell.self, forCellReuseIdentifier: MusicListTableViewCell.identifier)
        tableView.separatorStyle = .none
        tableView.rowHeight = 60
        return tableView
    }()
    
    private lazy var topControlsSeparator: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.lightGray.withAlphaComponent(0.5)
        return view
    }()
    
    private lazy var originalSoundButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "original_sound_on"), for: .normal)
        button.setImage(UIImage(named: "original_sound_off"), for: .selected)
        button.setTitle("原声开", for: .normal)
        button.setTitle("原声关", for: .selected)
        button.setTitleColor(.black, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14)
        
        // 设置按钮为垂直布局（图标在上，文字在下）
        button.contentHorizontalAlignment = .center
        button.contentVerticalAlignment = .center
        
        // 图片和文字的间距
        let spacing: CGFloat = 8
        
        // 设置图片在上，文字在下
        button.titleEdgeInsets = UIEdgeInsets(top: spacing, left: -button.imageView!.frame.size.width, bottom: -button.imageView!.frame.size.height, right: 0)
        button.imageEdgeInsets = UIEdgeInsets(top: -button.titleLabel!.intrinsicContentSize.height - spacing, left: 0, bottom: 0, right: -button.titleLabel!.intrinsicContentSize.width)
        
        button.addTarget(self, action: #selector(originalSoundButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var musicSoundButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "video_edit_music_on"), for: .normal)
        button.setImage(UIImage(named: "video_edit_music_off"), for: .selected)
        button.setTitle("配乐开", for: .normal)
        button.setTitle("配乐关", for: .selected)
        button.setTitleColor(.black, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14)
        
        // 设置按钮为垂直布局（图标在上，文字在下）
        button.contentHorizontalAlignment = .center
        button.contentVerticalAlignment = .center
        
        // 图片和文字的间距
        let spacing: CGFloat = 8
        
        // 设置图片在上，文字在下
        button.titleEdgeInsets = UIEdgeInsets(top: spacing, left: -button.imageView!.frame.size.width, bottom: -button.imageView!.frame.size.height, right: 0)
        button.imageEdgeInsets = UIEdgeInsets(top: -button.titleLabel!.intrinsicContentSize.height - spacing, left: 0, bottom: 0, right: -button.titleLabel!.intrinsicContentSize.width)
        
        button.addTarget(self, action: #selector(musicSoundButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var volumeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "video_edit_volume_control"), for: .normal)
        button.setTitle("音量", for: .normal)
        button.setTitleColor(.black, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14)
        
        // 设置按钮为垂直布局（图标在上，文字在下）
        button.contentHorizontalAlignment = .center
        button.contentVerticalAlignment = .center
        
        // 图片和文字的间距
        let spacing: CGFloat = 8
        
        // 设置图片在上，文字在下
        button.titleEdgeInsets = UIEdgeInsets(top: spacing, left: -button.imageView!.frame.size.width, bottom: -button.imageView!.frame.size.height, right: 0)
        button.imageEdgeInsets = UIEdgeInsets(top: -button.titleLabel!.intrinsicContentSize.height - spacing, left: 0, bottom: 0, right: -button.titleLabel!.intrinsicContentSize.width)
        
        button.addTarget(self, action: #selector(volumeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var volumeAdjustmentPopupView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.isHidden = true
        
        // 只设置左上角和右上角的圆角
        view.layer.cornerRadius = 10
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        view.layer.masksToBounds = true
        
        // 添加标题
        let titleLabel = UILabel()
        titleLabel.text = "音乐调节"
        titleLabel.textColor = UIColor.black.withAlphaComponent(0.85)
        titleLabel.textAlignment = .center
        titleLabel.font = .systemFont(ofSize: 16, weight: .medium)
        view.addSubview(titleLabel)
        
        // 添加关闭按钮
        let closeButton = UIButton(type: .custom)
        closeButton.setImage(UIImage(named: "icon_close") ?? UIImage(systemName: "xmark"), for: .normal)
        closeButton.tintColor = .gray
        closeButton.addTarget(self, action: #selector(hideVolumePopup), for: .touchUpInside)
        view.addSubview(closeButton)
        
        // 创建原声音量控制器
        let originalVolumeControl = VerticalVolumeControl(
            title: "原声",
            icon: UIImage(named: "original_sound_w") ?? UIImage(systemName: "speaker.wave.2"),
            initialVolume: originalSoundVolume
        )
        originalVolumeControl.onVolumeChanged = { [weak self] volume in
            self?.originalSoundVolume = volume
            self?.delegate?.musicPanelDidAdjustOriginalVolume(volume / 100.0)
        }
        view.addSubview(originalVolumeControl)
        
        // 创建配乐音量控制器
        let musicVolumeControl = VerticalVolumeControl(
            title: "配乐",
            icon: UIImage(named: "video_edit_music_w") ?? UIImage(systemName: "music.note"),
            initialVolume: musicVolume
        )
        musicVolumeControl.onVolumeChanged = { [weak self] volume in
            self?.musicVolume = volume
            self?.delegate?.musicPanelDidAdjustMusicVolume(volume / 100.0)
        }
        view.addSubview(musicVolumeControl)
        
        // 底部进度条指示器
        let indicatorView = UIView()
        indicatorView.backgroundColor = .black
        indicatorView.layer.cornerRadius = 2
        view.addSubview(indicatorView)
        
        // 设置各组件布局
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.centerX.equalToSuperview()
        }
        
        closeButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(16)
            make.width.height.equalTo(24)
        }
        
        // 设置两个控制器的间距，使其均匀分布
        originalVolumeControl.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(72)
            make.centerX.equalToSuperview().multipliedBy(0.5) // 位于屏幕左半部分中心
            make.width.equalTo(50)
            make.height.equalTo(230) // 200(柱体) + 8 + 文字高度
        }
        
        musicVolumeControl.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(72)
            make.centerX.equalToSuperview().multipliedBy(1.5) // 位于屏幕右半部分中心
            make.width.equalTo(50)
            make.height.equalTo(230) // 200(柱体) + 8 + 文字高度
        }
        
        indicatorView.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-8)
            make.centerX.equalToSuperview()
            make.width.equalTo(40)
            make.height.equalTo(4)
        }
        
        return view
    }()
    
    private lazy var musicRefreshControl: UIRefreshControl = {
        let refreshControl = UIRefreshControl()
        refreshControl.addTarget(self, action: #selector(refreshMusicData(_:)), for: .valueChanged)
        return refreshControl
    }()
    
    // 添加背景蒙版属性
    private lazy var volumeOverlay: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        view.isHidden = true
        view.alpha = 0
        
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(hideVolumePopup))
        view.addGestureRecognizer(tapGesture)
        
        return view
    }()
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    override func didMoveToSuperview() {
        super.didMoveToSuperview()
        if superview != nil && !didSetupConstraints {
            setupConstraints()
            didSetupConstraints = true

            // 在约束设置完成后更新指示器的位置和大小，避免与初始固定宽度约束冲突
            updateMusicSegmentSelection(selectedButton: allMusicSegmentButton, animated: false)
        }
    }
    
    override func safeAreaInsetsDidChange() {
        if #available(iOS 11.0, *) {
            super.safeAreaInsetsDidChange()
            bottomSafeAreaInset = safeAreaInsets.bottom
            updateConstraintsForSafeArea()
        }
    }
    
    // 更新约束以考虑安全区域
    private func updateConstraintsForSafeArea() {
        let bottomControlsHeight: CGFloat = 70 // 底部控制区高度
        let safeAreaPadding: CGFloat = bottomSafeAreaInset > 0 ? bottomSafeAreaInset : 10 // 如果有安全区域使用它，否则使用默认间距
        
        // 更新分隔线位置
        topControlsSeparator.snp.updateConstraints { make in
            make.bottom.equalToSuperview().offset(-(bottomControlsHeight + safeAreaPadding))
        }
        
        // 更新底部按钮容器位置，而不是直接更新originalSoundButton
        for subview in subviews {
            if let buttonsContainer = subview as? UIView, buttonsContainer.backgroundColor == .clear,
               buttonsContainer.subviews.contains(originalSoundButton) {
                buttonsContainer.snp.updateConstraints { make in
                    make.top.equalTo(topControlsSeparator.snp.bottom).offset(15)
                }
                break
            }
        }
        
        // 强制刷新布局
        setNeedsLayout()
        layoutIfNeeded()
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .white
        
        addSubview(musicSearchTextField)
        addSubview(allMusicSegmentButton)
        addSubview(favoriteMusicSegmentButton)
        addSubview(musicSegmentIndicatorView)
        addSubview(musicListTableView)
        addSubview(topControlsSeparator)
        
        // 添加蒙版和音量调节弹窗
        addSubview(volumeOverlay)
        addSubview(volumeAdjustmentPopupView)
        
        addSubview(originalSoundButton)
        addSubview(musicSoundButton)
        addSubview(volumeButton)
        
        if #available(iOS 10.0, *) {
            musicListTableView.refreshControl = musicRefreshControl
        } else {
            musicListTableView.addSubview(musicRefreshControl)
        }
    }
    
    private func setupConstraints() {
        let padding: CGFloat = 15
        let smallPadding: CGFloat = 8
        let buttonHeight: CGFloat = 36
        let segmentButtonWidth: CGFloat = 60
        let bottomControlsHeight: CGFloat = 70 // 增加底部控制区高度
        let separatorHeight: CGFloat = 1
        
        musicSearchTextField.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(padding)
            make.leading.equalToSuperview().offset(padding)
            make.height.equalTo(buttonHeight)
            make.trailing.equalToSuperview().offset(-padding)
        }
        
        allMusicSegmentButton.snp.makeConstraints { make in
            make.top.equalTo(musicSearchTextField.snp.bottom).offset(padding)
            make.leading.equalToSuperview().offset(padding)
            make.width.equalTo(segmentButtonWidth)
            make.height.equalTo(30)
        }
        
        favoriteMusicSegmentButton.snp.makeConstraints { make in
            make.centerY.equalTo(allMusicSegmentButton)
            make.leading.equalTo(allMusicSegmentButton.snp.trailing).offset(smallPadding)
            make.width.equalTo(segmentButtonWidth)
            make.height.equalTo(30)
        }
        
        musicSegmentIndicatorView.snp.makeConstraints { make in
            make.bottom.equalTo(allMusicSegmentButton.snp.bottom)
            make.height.equalTo(2)
            make.width.equalTo(segmentButtonWidth - 10)
            make.centerX.equalTo(allMusicSegmentButton)
        }
        
        topControlsSeparator.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(separatorHeight)
            make.bottom.equalToSuperview().offset(-bottomControlsHeight) // 初始值，稍后会基于安全区域更新
        }
        
        musicListTableView.snp.makeConstraints { make in
            make.top.equalTo(allMusicSegmentButton.snp.bottom).offset(smallPadding)
            make.leading.equalToSuperview().offset(padding)
            make.trailing.equalToSuperview().offset(-padding)
            make.bottom.equalTo(topControlsSeparator.snp.top).offset(-smallPadding)
        }
        
        // 设置三个按钮，考虑左右边距各24pt，并在剩余空间平分宽度
        let horizontalMargin: CGFloat = 24 // 左右边距各24pt
        
        // 使用容器视图包含这三个按钮，设置容器左右边距
        let buttonsContainer = UIView()
        buttonsContainer.backgroundColor = .clear
        addSubview(buttonsContainer)
        
        // 设置容器约束
        buttonsContainer.snp.makeConstraints { make in
            make.top.equalTo(topControlsSeparator.snp.bottom).offset(15)
            make.left.equalToSuperview().offset(horizontalMargin)
            make.right.equalToSuperview().offset(-horizontalMargin)
            make.height.equalTo(buttonHeight)
        }
        
        // 将按钮添加到容器中
        buttonsContainer.addSubview(originalSoundButton)
        buttonsContainer.addSubview(musicSoundButton)
        buttonsContainer.addSubview(volumeButton)
        
        // 设置按钮在容器内平分宽度
        originalSoundButton.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(1.0/3.0) // 容器宽度的三分之一
        }
        
        musicSoundButton.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(originalSoundButton.snp.right)
            make.width.equalTo(originalSoundButton) // 与第一个按钮宽度相同
        }
        
        volumeButton.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(musicSoundButton.snp.right)
            make.right.equalToSuperview()
            make.width.equalTo(originalSoundButton) // 与第一个按钮宽度相同
        }
        
        volumeAdjustmentPopupView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(342)
            make.bottom.equalToSuperview()
        }
        
        // 添加蒙版约束
        volumeOverlay.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - Public Methods
    func loadMusicData(isFavorites: Bool, refreshing: Bool = false) {
        guard !isLoadingMusic else { return }
        if !refreshing && !canLoadMoreMusic {
            self.musicRefreshControl.endRefreshing()
            return
        }
        
        isLoadingMusic = true
        if refreshing {
            currentMusicPage = 0
            canLoadMoreMusic = true
        }
        
        print("Loading music from API: Page \(currentMusicPage), Favorites: \(isFavorites)")
        
        APIManager.shared.getVideoEditMusicList(collect: isFavorites, page: currentMusicPage, size: musicPageSize, keywords: currentSearchKeywords) { [weak self] result in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                self.isLoadingMusic = false
                self.musicRefreshControl.endRefreshing()
                
                switch result {
                case .success(let response):
                    // Access the list via response.data.list due to updated VideoEditMusicListResponse structure
                    let musicListData = response.data?.list ?? []
                    
                    let fetchedItems: [MusicItem] = musicListData.compactMap { apiItem -> MusicItem? in
                        // apiItem is VideoEditMusicListData
                        print("--- Processing apiItem ---") // DEBUG
                        print("apiItem.id: \(String(describing: apiItem.id))") // DEBUG
                        print("apiItem.name: \(String(describing: apiItem.name))") // DEBUG
                        print("apiItem.musicAuthor: \(String(describing: apiItem.musicAuthor))") // DEBUG
                        print("apiItem.describeImg: \(String(describing: apiItem.describeImg))") // DEBUG
                        print("apiItem.contentFile: \(String(describing: apiItem.contentFile))") // DEBUG
                        print("apiItem.collect: \(String(describing: apiItem.collect))") // DEBUG
                        
                        // Break down the MusicItem creation
                        let idString: String
                        if let idInt = apiItem.id {
                            idString = String(idInt)
                        } else {
                            // This case should ideally not happen if API guarantees ID
                            print("Warning: apiItem.id was nil. Generating UUID.") //DEBUG
                            idString = UUID().uuidString
                        }
                        
                        let title = apiItem.name ?? "Unknown Title"
                        let artist = apiItem.musicAuthor ?? "Unknown Artist"
                        let duration = self.formatDuration(apiItem.duration)
                        let artworkImage = UIImage(systemName: "music.note") // Placeholder for actual UIImage
                        let artworkURL = apiItem.describeImg // Get the URL string from API
                        let isFavorite = (apiItem.collect == "1")
                        let contentFile = apiItem.contentFile ?? ""
                        
                        if title == "Unknown Title" && artist == "Unknown Artist" {
                            print("Warning: apiItem.name and apiItem.musicAuthor were both nil.") // DEBUG
                        }
                        
                        guard !contentFile.isEmpty else {
                            print("Warning: Skipping item with empty contentFile. Title: \(title), apiItem.contentFile was \(String(describing: apiItem.contentFile)))") // DEBUG
                            return nil
                        }
                        print("Successfully mapped apiItem to MusicItem: \(title)") // DEBUG
                        return MusicItem(
                            id: idString,
                            title: title,
                            artist: artist,
                            duration: duration,
                            artwork: artworkImage, // Keep using placeholder UIImage for now
                            isPlaying: false,
                            isFavorite: isFavorite,
                            contentFile: contentFile,
                            localFileURL: nil,
                            downloadState: .notDownloaded,
                            artworkURLString: artworkURL // Pass the URL string
                        )
                    }
                    
                    var processedItems: [MusicItem] = []
                    for var item in fetchedItems {
                        if musicFileExistsInCache(for: item.contentFile) {
                            item.localFileURL = localMusicFilePath(for: item.contentFile)
                            item.downloadState = .downloaded
                        } else {
                            item.downloadState = .notDownloaded
                        }
                        processedItems.append(item)
                    }
                    print("MusicPanelView - Processed items count: \(processedItems.count)") // DEBUG
                    
                    // 新增：去重，避免与现有musicItems重复（根据id或localFileURL）
                    let existingIds = Set(self.musicItems.map { $0.id })
                    let existingPaths = Set(self.musicItems.compactMap { $0.localFileURL?.path })
                    let uniqueItems = processedItems.filter { item in
                        !existingIds.contains(item.id) && (item.localFileURL?.path == nil || !existingPaths.contains(item.localFileURL!.path))
                    }
                    
                    if refreshing {
                        self.musicItems = processedItems
                    } else {
                        self.musicItems.append(contentsOf: uniqueItems)
                    }
                    print("MusicPanelView - Total musicItems count after update: \(self.musicItems.count)") // DEBUG
                    
                    if fetchedItems.count < self.musicPageSize {
                        self.canLoadMoreMusic = false
                    } else {
                        self.currentMusicPage += 1
                    }
                    
                    if self.musicItems.isEmpty {
                        print("Music list is empty after API call.")
                        // TODO: 显示空状态视图
                    }
                    
                case .failure(let error):
                    print("Error fetching music list: \(error.localizedDescription)")
                    // TODO: Handle API error (e.g., show an error message to the user)
                    if refreshing { // If it was a refresh, clear existing items or show error state
                        self.musicItems = []
                    }
                    // Optionally, set canLoadMoreMusic to false or handle retry logic
                    self.canLoadMoreMusic = false // Stop further loading on error for now
                }
            }
        }
    }
    
    func startDownload(for item: MusicItem, at indexPath: IndexPath) {
        // item.contentFile is a non-optional String.
        let initialUrlString = item.contentFile
        
        guard !initialUrlString.isEmpty, let initialUrl = URL(string: initialUrlString) else {
            // Simplified print statement to avoid complex escaping and ensure clarity.
            print("🔴 Invalid initial URL string. Provided: '\\(initialUrlString)'. Item: \\(item.title)")
            self.updateMusicItemDownloadState(at: indexPath, newState: .failed)
            return
        }
        
        if case .downloaded = item.downloadState {
            print("ℹ️ Already downloaded: \\(item.title)")
            return
        }
        if case .downloading = item.downloadState {
            print("ℹ️ Already downloading: \\(item.title)")
            return
        }
        
        print("⬇️ Preparing to download for: \'\\(item.title)\' from initial URL: \\(initialUrl.absoluteString)")
        self.updateMusicItemDownloadState(at: indexPath, newState: .downloading(progress: 0.0)) // Indicate process has started
        
        var request = URLRequest(url: initialUrl)
        // Try with a common browser User-Agent
        request.setValue("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15", forHTTPHeaderField: "User-Agent")
        
        let dataTask = URLSession.shared.dataTask(with: request) { [weak self] data, response, error in
            guard let self = self else { return }
            
            // ALWAYS check for an error from the dataTask itself
            if let error = error {
                print("🔴 DataTask error for initial URL \'\(initialUrl.absoluteString)\' (\(item.title)): \(error.localizedDescription)")
                DispatchQueue.main.async {
                    // Check if item still exists and is the same
                    if indexPath.row < self.musicItems.count && self.musicItems[indexPath.row].id == item.id {
                        self.updateMusicItemDownloadState(at: indexPath, newState: .failed)
                    }
                }
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                print("🔴 No HTTPURLResponse received for initial URL \'\\(initialUrl.absoluteString)\' (\(item.title)). This is unusual.")
                DispatchQueue.main.async {
                    if indexPath.row < self.musicItems.count && self.musicItems[indexPath.row].id == item.id {
                        self.updateMusicItemDownloadState(at: indexPath, newState: .failed)
                    }
                }
                return
            }
            
            print("ℹ️ Initial response from \'\\(initialUrl.absoluteString)\': Status Code \\(httpResponse.statusCode) for item \'\\(item.title)\'")
            
            // Check for redirect status codes (301, 302, 303, 307, 308)
            if (300...399).contains(httpResponse.statusCode) {
                if let locationHeader = httpResponse.allHeaderFields["Location"] as? String, let redirectedUrl = URL(string: locationHeader) {
                    print("ℹ️ Redirected to: \\(redirectedUrl.absoluteString) for item \'\\(item.title)\'")
                    // Now, start the actual download with the redirected URL
                    self.performActualDownload(with: redirectedUrl, for: item, at: indexPath, originalContentFileKey: initialUrlString)
                } else {
                    print("🔴 Redirect response (status \\(httpResponse.statusCode)) but no valid Location header found for \'\\(item.title)\'.")
                    if let locationAny = httpResponse.allHeaderFields["Location"] {
                        print("🔴 Location header was: \\(locationAny)")
                    }
                    DispatchQueue.main.async {
                        if indexPath.row < self.musicItems.count && self.musicItems[indexPath.row].id == item.id {
                            self.updateMusicItemDownloadState(at: indexPath, newState: .failed)
                        }
                    }
                }
            } else if httpResponse.statusCode == 200 {
                print("⚠️ Received 200 OK from initial URL \'\\(initialUrl.absoluteString)\' for \'\\(item.title)\'. Attempting to download directly.")
                // This might be the actual file if User-Agent helped, or it could be an HTML error page.
                self.performActualDownload(with: initialUrl, for: item, at: indexPath, originalContentFileKey: initialUrlString)
            } else {
                print("🔴 Non-redirect, non-200 HTTP status code \\(httpResponse.statusCode) from initial URL \'\\(initialUrl.absoluteString)\' for \'\\(item.title)\'.")
                DispatchQueue.main.async {
                    if indexPath.row < self.musicItems.count && self.musicItems[indexPath.row].id == item.id {
                        self.updateMusicItemDownloadState(at: indexPath, newState: .failed)
                    }
                }
            }
        }
        dataTask.resume()
    }
    
    // Helper function for the actual download
    private func performActualDownload(with url: URL, for item: MusicItem, at indexPath: IndexPath, originalContentFileKey: String) {
        print("🚀 Starting actual download for: \'\\(item.title)\' from URL: \\(url.absoluteString)")
        
        let downloadTask = URLSession.shared.downloadTask(with: url) { [weak self] tempLocalURL, response, error in
            guard let self = self else { return }
            
            // Ensure UI updates and item access are safe, especially if indexPath or item could have changed
            guard indexPath.row < self.musicItems.count, self.musicItems[indexPath.row].id == item.id else {
                print("⚠️ Download callback for stale item or indexPath: \'\\(item.title)\'. List may have changed.")
                // Clean up temp file if it exists, though URLSession usually handles this on task invalidation.
                if let tempLocalURL = tempLocalURL { try? FileManager.default.removeItem(at: tempLocalURL) }
                return
            }
            
            if let error = error {
                print("🔴 Actual download error for \'\\(item.title)\' from URL \'\\(url.absoluteString)\': \\(error.localizedDescription)")
                if let httpResponse = response as? HTTPURLResponse {
                    print("🔴 HTTP Status Code: \\(httpResponse.statusCode)")
                    print("🔴 HTTP Headers: \\(httpResponse.allHeaderFields)")
                }
                DispatchQueue.main.async {
                    self.updateMusicItemDownloadState(at: indexPath, newState: .failed)
                }
                return
            }
            
            guard let tempLocalURL = tempLocalURL else {
                print("🔴 Actual download error for \'\\(item.title)\': tempLocalURL is nil and no error reported.")
                if let httpResponse = response as? HTTPURLResponse {
                    print("🔴 HTTP Status Code: \\(httpResponse.statusCode) for URL \'\\(url.absoluteString)\'")
                    print("🔴 HTTP Headers: \\(httpResponse.allHeaderFields)")
                }
                DispatchQueue.main.async {
                    self.updateMusicItemDownloadState(at: indexPath, newState: .failed)
                }
                return
            }
            
            guard let permanentFileURL = localMusicFilePath(for: originalContentFileKey) else {
                print("🔴 Could not create permanent file path for \'\\(item.title)\' using cache key: \'\\(originalContentFileKey)\'")
                DispatchQueue.main.async {
                    self.updateMusicItemDownloadState(at: indexPath, newState: .failed)
                }
                return
            }
            
            do {
                if FileManager.default.fileExists(atPath: permanentFileURL.path) {
                    try FileManager.default.removeItem(at: permanentFileURL)
                }
                try FileManager.default.moveItem(at: tempLocalURL, to: permanentFileURL)
                print("✅ Successfully downloaded and moved \'\(item.title)\' to \(permanentFileURL.path)")
                
                // Log MIME type and expected content length upon successful download and move
                if let httpResponse = response as? HTTPURLResponse {
                    print("ℹ️ Downloaded file ('\(item.title)') HTTP Status Code: \(httpResponse.statusCode)")
                    print("ℹ️ Downloaded file ('\(item.title)') MIME Type: \(httpResponse.mimeType ?? "Unknown")")
                    print("ℹ️ Downloaded file ('\(item.title)') Expected Content Length: \\(httpResponse.expectedContentLength)")
                }
                
                DispatchQueue.main.async {
                    // Re-fetch item from array before modification, as 'item' is a copy
                    var updatedItem = self.musicItems[indexPath.row]
                    updatedItem.localFileURL = permanentFileURL
                    updatedItem.downloadState = .downloaded
                    self.musicItems[indexPath.row] = updatedItem
                    self.musicListTableView.reloadRows(at: [indexPath], with: .automatic)
                }
            } catch {
                print("🔴 Error moving downloaded file for \'\(item.title)\': \(error.localizedDescription)")
                print("🔴 Failed to move from: \(tempLocalURL.path) to: \(permanentFileURL.path)")
                DispatchQueue.main.async {
                    self.updateMusicItemDownloadState(at: indexPath, newState: .failed)
                }
            }
        }
        downloadTask.resume()
    }
    
    // MARK: - Private Methods

    /// 格式化音乐时长
    /// - Parameter durationInSeconds: 时长（秒）
    /// - Returns: 格式化后的时长字符串，如 "02:30"
    private func formatDuration(_ durationInSeconds: Int?) -> String {
        guard let duration = durationInSeconds, duration > 0 else {
            return "00:00"
        }

        let minutes = duration / 60
        let seconds = duration % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }

    // MARK: - 搜索相关方法

    @objc private func searchTextFieldDidChange(_ textField: UITextField) {
        // 取消之前的搜索定时器
        searchTimer?.invalidate()

        // 设置新的定时器，延迟0.5秒执行搜索
        searchTimer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: false) { [weak self] _ in
            self?.performSearch(with: textField.text)
        }
    }

    private func performSearch(with keywords: String?) {
        let trimmedKeywords = keywords?.trimmingCharacters(in: .whitespacesAndNewlines)

        // 如果搜索关键词没有变化，不重复搜索
        if currentSearchKeywords == trimmedKeywords {
            return
        }

        currentSearchKeywords = trimmedKeywords

        // 重置分页
        currentMusicPage = 0
        canLoadMoreMusic = true

        // 加载音乐数据
        loadMusicData(isFavorites: favoriteMusicSegmentButton.isSelected, refreshing: true)
    }

    private func updateMusicSegmentSelection(selectedButton: UIButton, animated: Bool = true) {
        allMusicSegmentButton.isSelected = (selectedButton == allMusicSegmentButton)
        favoriteMusicSegmentButton.isSelected = (selectedButton == favoriteMusicSegmentButton)
        
        musicSegmentIndicatorView.snp.remakeConstraints { make in
            make.bottom.equalTo(selectedButton.snp.bottom)
            make.height.equalTo(2)
            make.width.equalTo(selectedButton.snp.width).multipliedBy(0.8)
            make.centerX.equalTo(selectedButton)
        }
        
        if animated {
            UIView.animate(withDuration: 0.3) {
                self.layoutIfNeeded()
            }
        } else {
            self.layoutIfNeeded()
        }
    }
    
    private func showVolumePopup() {
        guard !isVolumePopupShowing else { return }
        isVolumePopupShowing = true
        
        // 先显示蒙版
        volumeOverlay.isHidden = false
        bringSubviewToFront(volumeOverlay)
        
        // 再显示弹窗
        volumeAdjustmentPopupView.isHidden = false
        bringSubviewToFront(volumeAdjustmentPopupView)
        
        // 查找并更新音量控制器的值
        for subview in volumeAdjustmentPopupView.subviews {
            if let originalControl = subview as? VerticalVolumeControl, originalControl.titleLabel.text?.hasPrefix("原声") == true {
                originalControl.setVolume(originalSoundVolume)
            } else if let musicControl = subview as? VerticalVolumeControl, musicControl.titleLabel.text?.hasPrefix("配乐") == true {
                musicControl.setVolume(musicVolume)
            }
        }
        
        // 动画显示
        volumeOverlay.alpha = 0
        volumeAdjustmentPopupView.alpha = 0
        
        UIView.animate(withDuration: 0.2) {
            self.volumeOverlay.alpha = 1
            self.volumeAdjustmentPopupView.alpha = 1
        }
    }
    
    @objc private func hideVolumePopup() {
        guard isVolumePopupShowing else { return }
        
        UIView.animate(withDuration: 0.2, animations: {
            self.volumeOverlay.alpha = 0
            self.volumeAdjustmentPopupView.alpha = 0
        }) { _ in
            self.volumeOverlay.isHidden = true
            self.volumeAdjustmentPopupView.isHidden = true
            self.isVolumePopupShowing = false
        }
    }
    
    private func updateMusicItemDownloadState(at indexPath: IndexPath, newState: DownloadState) {
        guard indexPath.row < self.musicItems.count else { return }
        self.musicItems[indexPath.row].downloadState = newState
        self.musicListTableView.reloadRows(at: [indexPath], with: .none)
    }
    
    private func toggleFavoriteStatus(at indexPath: IndexPath) {
        guard indexPath.row < musicItems.count else {
            print("toggleFavoriteStatus: indexPath out of bounds.")
            return
        }

        let item = musicItems[indexPath.row] // Get a copy of the item model
        let newFavoriteState = !item.isFavorite // Determine the target state
        var newFavorite = 0
        // Optionally, show a loading indicator on the cell's favorite button here
        if newFavoriteState {
            newFavorite = 1
        } else {
            newFavorite = 2
        }

        // 防止重复点击导致的问题，先禁用按钮
        if let cell = musicListTableView.cellForRow(at: indexPath) as? MusicListTableViewCell {
            cell.favoriteButton.isEnabled = false
        }

        APIManager.shared.videoEditCollectingMusic(id: Int(item.id) ?? 0, operate: newFavorite) { [weak self] result in
            DispatchQueue.main.async {
                guard let self = self else { return }

                // 重新启用按钮
                if let cell = self.musicListTableView.cellForRow(at: indexPath) as? MusicListTableViewCell {
                    cell.favoriteButton.isEnabled = true
                }

                // Validate indexPath again, as the list might have changed
                guard indexPath.row < self.musicItems.count, self.musicItems[indexPath.row].id == item.id else {
                    print("Warning: Data inconsistency or list changed during favorite toggle for item ID \(item.id). Current item at indexPath is \(self.musicItems.indices.contains(indexPath.row) ? self.musicItems[indexPath.row].id : "nil"). Refreshing list.")
                    self.loadMusicData(isFavorites: self.favoriteMusicSegmentButton.isSelected, refreshing: true)
                    return
                }

                var currentItemInTable = self.musicItems[indexPath.row]

                switch result {
                case .success:
                    print("Successfully updated favorite status on server for: \(currentItemInTable.title) to \(newFavoriteState)")
                    currentItemInTable.isFavorite = newFavoriteState

                    let originalItemForDelegate = currentItemInTable // Capture for delegate before potential removal

                    if self.favoriteMusicSegmentButton.isSelected && !newFavoriteState {
                        // Item was unfavorited while in the "Favorites" tab
                        // 先通知委托，再删除行，避免indexPath失效
                        self.delegate?.musicPanelDidToggleFavorite(originalItemForDelegate, at: indexPath)

                        // 使用performBatchUpdates确保UI更新的原子性
                        self.musicListTableView.performBatchUpdates({
                            self.musicItems.remove(at: indexPath.row)
                            self.musicListTableView.deleteRows(at: [indexPath], with: .automatic)
                        }, completion: { _ in
                            // 删除完成后的处理
                            print("Successfully removed unfavorited item from favorites list")
                        })
                    } else {
                        // Item was favorited, or unfavorited in "All" tab, or item still exists
                        self.musicItems[indexPath.row] = currentItemInTable
                        self.musicListTableView.reloadRows(at: [indexPath], with: .automatic)

                        // Notify delegate with the item that reflects the new state
                        self.delegate?.musicPanelDidToggleFavorite(originalItemForDelegate, at: indexPath)
                    }

                case .failure(let error):
                    print("Error updating favorite status for \(currentItemInTable.title): \(error.localizedDescription)")
                    // TODO: Show an error message to the user (e.g., using a toast or alert)
                    // Revert favorite button UI if it was changed optimistically, or simply reload.
                    // Since we didn't change local data before API response, no data revert is needed.
                    // However, reloading the row ensures the UI is consistent with the (unchanged) model state.
                    if indexPath.row < self.musicItems.count {
                        self.musicListTableView.reloadRows(at: [indexPath], with: .none)
                    }
                }
            }
        }
    }
    
    @objc private func musicSegmentButtonTapped(_ sender: UIButton) {
        updateMusicSegmentSelection(selectedButton: sender)
        currentMusicPage = 0
        canLoadMoreMusic = true
        self.musicItems = []
        loadMusicData(isFavorites: sender == favoriteMusicSegmentButton, refreshing: true)
    }
    
    @objc private func originalSoundButtonTapped() {
        originalSoundButton.isSelected = !originalSoundButton.isSelected
        let isOn = !originalSoundButton.isSelected
        print("原声状态: \(isOn ? "开" : "关")")
        delegate?.musicPanelDidToggleOriginalSound(isOn)
    }
    
    @objc private func musicSoundButtonTapped() {
        musicSoundButton.isSelected = !musicSoundButton.isSelected
        let isOn = !musicSoundButton.isSelected
        print("配乐状态: \(isOn ? "开" : "关")")
        delegate?.musicPanelDidToggleMusicSound(isOn)
    }
    
    @objc private func volumeButtonTapped() {
        print("音量按钮被点击")
        if isVolumePopupShowing {
            hideVolumePopup()
        } else {
            showVolumePopup()
        }
    }
    
    @objc private func refreshMusicData(_ sender: UIRefreshControl) {
        currentMusicPage = 0
        canLoadMoreMusic = true
        let isFav = favoriteMusicSegmentButton.isSelected
        loadMusicData(isFavorites: isFav, refreshing: true)
    }
    
    // 添加布局方法，在按钮添加到视图后调整EdgeInsets
    private func adjustButtonLayout() {
        // 原声按钮
        if let imageSize = originalSoundButton.imageView?.image?.size,
           let titleSize = originalSoundButton.titleLabel?.intrinsicContentSize {
            let spacing: CGFloat = 8
            originalSoundButton.titleEdgeInsets = UIEdgeInsets(
                top: imageSize.height + spacing,
                left: -imageSize.width,
                bottom: 0,
                right: 0
            )
            originalSoundButton.imageEdgeInsets = UIEdgeInsets(
                top: -titleSize.height - spacing,
                left: 0,
                bottom: 0,
                right: -titleSize.width
            )
        }
        
        // 配乐按钮
        if let imageSize = musicSoundButton.imageView?.image?.size,
           let titleSize = musicSoundButton.titleLabel?.intrinsicContentSize {
            let spacing: CGFloat = 8
            musicSoundButton.titleEdgeInsets = UIEdgeInsets(
                top: imageSize.height + spacing,
                left: -imageSize.width,
                bottom: 0,
                right: 0
            )
            musicSoundButton.imageEdgeInsets = UIEdgeInsets(
                top: -titleSize.height - spacing,
                left: 0,
                bottom: 0,
                right: -titleSize.width
            )
        }
        
        // 音量按钮
        if let imageSize = volumeButton.imageView?.image?.size,
           let titleSize = volumeButton.titleLabel?.intrinsicContentSize {
            let spacing: CGFloat = 8
            volumeButton.titleEdgeInsets = UIEdgeInsets(
                top: imageSize.height + spacing,
                left: -imageSize.width,
                bottom: 0,
                right: 0
            )
            volumeButton.imageEdgeInsets = UIEdgeInsets(
                top: -titleSize.height - spacing,
                left: 0,
                bottom: 0,
                right: -titleSize.width
            )
        }
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        adjustButtonLayout()
    }
    
    // 新增：外部插入并选中BGM（去重、置顶、选中）
    func insertAndSelectBGM(_ bgm: MusicItem) {
        // 只设置selectedMusic，不直接操作musicItems
        self.selectedMusic = bgm
        updateSelectedMusicState()
    }
    
    private var isUpdatingSelectedState = false
    private func updateSelectedMusicState() {
        guard !isUpdatingSelectedState else { return }
        isUpdatingSelectedState = true
        for i in 0..<musicItems.count {
            if let selected = selectedMusic {
                musicItems[i].isPlaying = (musicItems[i].id == selected.id) ||
                (musicItems[i].localFileURL != nil && selected.localFileURL != nil && musicItems[i].localFileURL == selected.localFileURL)
            } else {
                musicItems[i].isPlaying = false
            }
        }
        musicListTableView.reloadData()
        isUpdatingSelectedState = false
    }
    
    // 外部调用：清除所有选中项
    func clearSelectedMusic() {
        selectedMusic = nil // 新增：清空选中项
        for i in 0..<musicItems.count {
            musicItems[i].isPlaying = false
        }
        musicListTableView.reloadData()
    }
    
    // 录制页专用配置：隐藏底部原声/配乐/音量按钮
    func configureForRecordMode(_ isRecord: Bool) {
        originalSoundButton.isHidden = isRecord
        musicSoundButton.isHidden = isRecord
        volumeButton.isHidden = isRecord
        topControlsSeparator.isHidden = isRecord
        volumeAdjustmentPopupView.isHidden = true
        volumeOverlay.isHidden = true
    }
}

// MARK: - Cache Management Helpers
private func getMusicCacheDirectory() -> URL? {
    guard let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
        return nil
    }
    let cacheDirectory = documentsDirectory.appendingPathComponent("MusicCache")
    if !FileManager.default.fileExists(atPath: cacheDirectory.path) {
        try? FileManager.default.createDirectory(at: cacheDirectory, withIntermediateDirectories: true, attributes: nil)
    }
    return cacheDirectory
}

// Helper extension for URL to get query parameters
extension URL {
    var queryParameters: [String: String]? {
        guard let components = URLComponents(url: self, resolvingAgainstBaseURL: true), let queryItems = components.queryItems else { return nil }
        return queryItems.reduce(into: [String: String]()) { (result, item) in
            result[item.name] = item.value
        }
    }
}

private func localMusicFilePath(for contentFileString: String) -> URL? {
    guard let cacheDirectory = getMusicCacheDirectory() else {
        print("🔴 Could not get/create music cache directory.")
        return nil
    }
    
    var fileNameCandidate = "cached_track"
    
    if let contentURL = URL(string: contentFileString) {
        if let id = contentURL.queryParameters?["id"], !id.isEmpty {
            fileNameCandidate = id
        } else {
            let lastPathComponent = contentURL.lastPathComponent
            if !lastPathComponent.isEmpty && lastPathComponent != "/" {
                fileNameCandidate = URL(fileURLWithPath: lastPathComponent).deletingPathExtension().lastPathComponent
            }
        }
    } else {
        print("⚠️ Could not parse contentFileString as URL: '\(contentFileString)'. Using default filename candidate.")
    }
    
    // Sanitize filename candidate (remove potentially problematic characters but keep basic structure)
    let allowedCharsForName = CharacterSet.alphanumerics.union(CharacterSet(charactersIn: "_-"))
    var sanitizedFileNameBase = fileNameCandidate.components(separatedBy: allowedCharsForName.inverted).joined()
    
    if sanitizedFileNameBase.isEmpty {
        sanitizedFileNameBase = UUID().uuidString // Fallback to UUID if sanitization results in empty string
        print("⚠️ Sanitized filename base was empty, using UUID: \(sanitizedFileNameBase)")
    }
    
    // Always ensure .mp3 extension, but only add if not already present in a reasonable form
    let finalFileName: String
    if sanitizedFileNameBase.lowercased().hasSuffix(".mp3") {
        // It already seems to have an mp3 suffix, use as is (after sanitization)
        finalFileName = sanitizedFileNameBase 
    } else {
        // Append .mp3 suffix
        finalFileName = "\(sanitizedFileNameBase).mp3"
    }
    
    print("ℹ️ Generated cache filename: '\(finalFileName)' for contentFile: '\(contentFileString)'")
    return cacheDirectory.appendingPathComponent(finalFileName)
}

private func musicFileExistsInCache(for contentFile: String) -> Bool {
    guard let localPath = localMusicFilePath(for: contentFile) else { return false }
    return FileManager.default.fileExists(atPath: localPath.path)
}

// MARK: - UITableViewDataSource, UITableViewDelegate
extension MusicPanelView: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        print("MusicPanelView - numberOfRowsInSection: \(self.musicItems.count)")
        return self.musicItems.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        print("MusicPanelView - cellForRowAt: \(indexPath.row)")
        guard let cell = tableView.dequeueReusableCell(withIdentifier: MusicListTableViewCell.identifier, for: indexPath) as? MusicListTableViewCell else {
            fatalError("Could not dequeue MusicListTableViewCell")
        }
        
        let item = self.musicItems[indexPath.row]
        cell.configure(with: item)
        
        // 设置标题颜色 - 选中为#FF8F1F，不选中为#000000透明度0.85
        if item.isPlaying {
            cell.titleLabel.textColor = UIColor(hex: "#FF8F1F")
        } else {
            cell.titleLabel.textColor = UIColor.black.withAlphaComponent(0.85)
        }
        
        cell.favoriteButtonAction = { [weak self] in
            guard let self = self else { return }
            self.toggleFavoriteStatus(at: indexPath)
        }
        
        cell.downloadButtonAction = { [weak self] in
            guard let self = self else { return }
            let currentItem = self.musicItems[indexPath.row]
            
            switch currentItem.downloadState {
            case .notDownloaded, .failed:
                self.startDownload(for: currentItem, at: indexPath)
            case .downloading:
                print("Cancel download tapped for: \(currentItem.title)")
                // TODO: 实现取消下载逻辑
            case .downloaded:
                break
            }
        }
        
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        guard indexPath.row < self.musicItems.count else {
            print("Error: didSelectRowAt indexPath out of bounds.")
            return
        }
        let selectedMusicItem = self.musicItems[indexPath.row]
        print("Selected music: \(selectedMusicItem.title), State: \(selectedMusicItem.downloadState), Playing: \(selectedMusicItem.isPlaying)")
        switch selectedMusicItem.downloadState {
        case .downloaded:
            guard selectedMusicItem.localFileURL != nil else {
                print("Error: Music item \(selectedMusicItem.title) is marked as downloaded but localFileURL is nil. Attempting to re-download.")
                self.startDownload(for: selectedMusicItem, at: indexPath)
                return
            }
            let wasPlayingThisItem = selectedMusicItem.isPlaying
            if wasPlayingThisItem {
                // 取消所有选中
                self.selectedMusic = nil
                updateSelectedMusicState()
                delegate?.musicPanelDidSelectMusic(nil)
                print("⏹️ 取消选中音乐: \(selectedMusicItem.title)")
                return
            }
            // 选中当前项
            self.selectedMusic = selectedMusicItem
            updateSelectedMusicState()
            delegate?.musicPanelDidSelectMusic(selectedMusicItem)
            print("▶️ 选中音乐: \(selectedMusicItem.title)")
        case .notDownloaded, .failed:
            print("Music '\(selectedMusicItem.title)' not downloaded. Starting download.")
            self.startDownload(for: selectedMusicItem, at: indexPath)
        case .downloading:
            print("Music '\(selectedMusicItem.title)' is currently downloading.")
        }
    }
    
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        // 接近列表末尾时加载更多数据
        let lastElement = self.musicItems.count - 1
        if indexPath.row >= lastElement - 4 && !isLoadingMusic && canLoadMoreMusic {
            let isFav = favoriteMusicSegmentButton.isSelected
            loadMusicData(isFavorites: isFav, refreshing: false)
        }
    }
}

// MARK: - UITextFieldDelegate
extension MusicPanelView: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        if textField == musicSearchTextField {
            textField.resignFirstResponder()
            performSearch(with: textField.text)
            return true
        }
        return false
    }
}
