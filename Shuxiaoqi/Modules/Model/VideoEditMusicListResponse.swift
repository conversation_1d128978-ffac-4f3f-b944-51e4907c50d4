//
//  VideoEditMusicListResponse.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yongsheng ye on 2025/5/14.
//

import SmartCodable

// MARK: - VideoEditMusicListData
struct VideoEditMusicListData: SmartCodable {
    var id: Int?
    var type: Int?
    var name: String?
    var musicAuthor: String?
    var duration: Int? // 音乐时长，单位为秒
    var info: String? // Assuming info is a String, adjust if needed
    var describeImg: String?
    var contentFile: String?
    var collect: String?
}

// New struct to match the nested "data" object in JSON
struct VideoEditMusicDataContainer: SmartCodable {
    var pageSize: Int?
    var pageNum: Int?
    var total: Int?
    var list: [VideoEditMusicListData]? // This is the actual music list
    var empty: Bool?
}

struct VideoEditMusicListResponse: SmartCodable {
    var status: Int = 0
    var errMsg: String?
    var msg: String?
    var data: VideoEditMusicDataContainer? // Changed to use the container struct

    // 计算属性：判断请求是否成功
    var isSuccess: Bool {
        return status == 200
    }

    // 计算属性：获取用于显示的消息
    public var displayMessage: String {
        if let message = msg, !message.isEmpty {
            return message
        }
        if let errorMsg = errMsg, !errorMsg.isEmpty {
            return errorMsg
        }
        return isSuccess ? "成功" : "未知错误"
    }
}
