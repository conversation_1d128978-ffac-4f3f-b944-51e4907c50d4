//
//  BeautyItem.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yongsheng ye on 2025/6/10.
//

import Foundation

// 美颜数据模型
struct BeautyItem {
    let id: Int
    let name: String
    let featureCode: String?
    let describeImg: String // 兼容旧字段，未选中状态图标
    let contentFile: String // 兼容旧字段，选中状态图标
    let checkedOfBeauty: String? // 新字段：选中状态图标
    let uncheckedOfBeauty: String? // 新字段：未选中状态图标
    var isSelected: Bool = false
    var intensity: Float = 0.0 // 美颜强度默认值改为0

    // 计算属性：获取选中状态图标URL
    var selectedImageURL: String {
        return checkedOfBeauty ?? contentFile
    }

    // 计算属性：获取未选中状态图标URL
    var normalImageURL: String {
        return uncheckedOfBeauty ?? describeImg
    }
}
